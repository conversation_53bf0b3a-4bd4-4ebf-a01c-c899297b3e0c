<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🃏 PetingGame - Types 簡化架構圖</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
        }

        .diagram-section {
            margin: 40px 0;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .diagram-title {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            text-align: center;
        }

        .mermaid-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            overflow-x: auto;
        }

        .tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #ecf0f1;
        }

        .tab {
            padding: 12px 24px;
            background: none;
            border: none;
            font-size: 1.1em;
            cursor: pointer;
            color: #7f8c8d;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab:hover {
            color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .info-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .info-card h3 {
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .info-card p {
            opacity: 0.9;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🃏 PetingGame Types 架構</h1>
            <p>TypeScript 類型系統簡化視圖</p>
        </div>

        <!-- Tab Navigation -->
        <div class="tabs">
            <button class="tab active" onclick="showTab('overview')">📊 總覽</button>
            <button class="tab" onclick="showTab('core')">🎯 核心類型</button>
            <button class="tab" onclick="showTab('systems')">⚙️ 系統模塊</button>
            <button class="tab" onclick="showTab('relationships')">🔗 關係圖</button>
        </div>

        <!-- Overview Tab -->
        <div id="overview" class="tab-content active">
            <div class="diagram-section">
                <h2 class="diagram-title">🏗️ 整體架構概覽</h2>
                <div class="mermaid-container">
                    <div class="mermaid">
                        graph TB
                            subgraph "🎯 Core Types"
                                A[Card<br/>卡牌基礎]
                                B[BattleCard<br/>戰鬥卡牌]
                                C[Team<br/>隊伍]
                                D[PlayerData<br/>玩家數據]
                            end
                            
                            subgraph "⚙️ System Modules"
                                E[Config<br/>配置系統]
                                F[Battle<br/>戰鬥系統]
                                G[Gacha<br/>抽卡系統]
                                H[UI<br/>界面系統]
                            end
                            
                            subgraph "🛠️ Utilities"
                                I[Utils<br/>工具函數]
                                J[Constants<br/>常量定義]
                            end
                            
                            A --> B
                            A --> C
                            C --> D
                            A --> E
                            B --> F
                            A --> G
                            F --> H
                            I -.-> A
                            I -.-> E
                            I -.-> F
                            I -.-> G
                            J -.-> A
                            J -.-> E
                            J -.-> F
                            J -.-> G
                            
                            style A fill:#e74c3c,stroke:#c0392b,color:#fff
                            style B fill:#e74c3c,stroke:#c0392b,color:#fff
                            style C fill:#e74c3c,stroke:#c0392b,color:#fff
                            style D fill:#e74c3c,stroke:#c0392b,color:#fff
                            style E fill:#f39c12,stroke:#e67e22,color:#fff
                            style F fill:#e67e22,stroke:#d35400,color:#fff
                            style G fill:#3498db,stroke:#2980b9,color:#fff
                            style H fill:#9b59b6,stroke:#8e44ad,color:#fff
                            style I fill:#2ecc71,stroke:#27ae60,color:#fff
                            style J fill:#2ecc71,stroke:#27ae60,color:#fff
                    </div>
                </div>
            </div>
        </div>

        <!-- Core Types Tab -->
        <div id="core" class="tab-content">
            <div class="diagram-section">
                <h2 class="diagram-title">🎯 核心類型結構</h2>
                <div class="mermaid-container">
                    <div class="mermaid">
                        classDiagram
                            class Card {
                                +string id
                                +string name
                                +CardRace race
                                +CardRarity rarity
                                +number level
                                +CardStats stats
                                +BattleTag[] battleTags
                                +ElementTag[] elementTags
                                +CardSkill[] skills
                            }
                            
                            class BattleCard {
                                +number currentHealth
                                +number actionBar
                                +StatusEffect[] buffs
                                +StatusEffect[] debuffs
                                +boolean isAlive
                                +number position
                                +string team
                            }
                            
                            class Team {
                                +string id
                                +string name
                                +Card[] cards
                                +number totalPower
                                +Date createdAt
                            }
                            
                            class PlayerData {
                                +string id
                                +number level
                                +number gold
                                +number diamonds
                                +Card[] ownedCards
                                +Team[] teams
                                +GameSettings settings
                            }
                            
                            Card <|-- BattleCard
                            Card --* Team
                            Team --* PlayerData
                            Card --* PlayerData
                            
                            class CardRarity {
                                <<enumeration>>
                                COMMON
                                RARE
                                EPIC
                                LEGENDARY
                                MYTHIC
                            }
                            
                            class CardRace {
                                <<enumeration>>
                                HUMAN
                                ELF
                                ORC
                                DRAGON
                                ANGEL
                                DEMON
                            }
                            
                            Card --> CardRarity
                            Card --> CardRace
                    </div>
                </div>
            </div>
        </div>

        <!-- Systems Tab -->
        <div id="systems" class="tab-content">
            <div class="diagram-section">
                <h2 class="diagram-title">⚙️ 系統模塊交互</h2>
                <div class="mermaid-container">
                    <div class="mermaid">
                        graph LR
                            subgraph "🔧 Config System"
                                A[CardConfig]
                                B[SkillConfig]
                                C[StageConfig]
                                D[GachaConfig]
                            end
                            
                            subgraph "⚔️ Battle System"
                                E[Battle]
                                F[BattleAction]
                                G[AIDecision]
                                H[BattleManager]
                            end
                            
                            subgraph "🎴 Gacha System"
                                I[GachaRequest]
                                J[GachaResult]
                                K[GachaManager]
                                L[GachaHistory]
                            end
                            
                            subgraph "🎨 UI System"
                                M[Theme]
                                N[ComponentProps]
                                O[AnimationConfig]
                                P[ResponsiveConfig]
                            end
                            
                            A --> E
                            B --> F
                            C --> E
                            D --> I
                            E --> M
                            F --> N
                            I --> J
                            J --> L
                            
                            style A fill:#f39c12,color:#fff
                            style B fill:#f39c12,color:#fff
                            style C fill:#f39c12,color:#fff
                            style D fill:#f39c12,color:#fff
                            style E fill:#e67e22,color:#fff
                            style F fill:#e67e22,color:#fff
                            style G fill:#e67e22,color:#fff
                            style H fill:#e67e22,color:#fff
                            style I fill:#3498db,color:#fff
                            style J fill:#3498db,color:#fff
                            style K fill:#3498db,color:#fff
                            style L fill:#3498db,color:#fff
                            style M fill:#9b59b6,color:#fff
                            style N fill:#9b59b6,color:#fff
                            style O fill:#9b59b6,color:#fff
                            style P fill:#9b59b6,color:#fff
                    </div>
                </div>
            </div>
        </div>

        <!-- Relationships Tab -->
        <div id="relationships" class="tab-content">
            <div class="diagram-section">
                <h2 class="diagram-title">🔗 類型依賴關係</h2>
                <div class="mermaid-container">
                    <div class="mermaid">
                        flowchart TD
                            A[index.ts<br/>核心類型定義] --> B[config.ts<br/>配置系統類型]
                            A --> C[battle.ts<br/>戰鬥系統類型]
                            A --> D[gacha.ts<br/>抽卡系統類型]
                            A --> E[ui.ts<br/>UI系統類型]
                            
                            B --> C
                            B --> D
                            C --> E
                            D --> E
                            
                            F[utils.ts<br/>工具函數] -.-> A
                            F -.-> B
                            F -.-> C
                            F -.-> D
                            F -.-> E
                            
                            G[constants.ts<br/>常量定義] -.-> A
                            G -.-> B
                            G -.-> C
                            G -.-> D
                            G -.-> E
                            
                            style A fill:#e74c3c,stroke:#c0392b,color:#fff
                            style B fill:#f39c12,stroke:#e67e22,color:#fff
                            style C fill:#e67e22,stroke:#d35400,color:#fff
                            style D fill:#3498db,stroke:#2980b9,color:#fff
                            style E fill:#9b59b6,stroke:#8e44ad,color:#fff
                            style F fill:#2ecc71,stroke:#27ae60,color:#fff
                            style G fill:#2ecc71,stroke:#27ae60,color:#fff
                    </div>
                </div>
            </div>
        </div>

        <!-- Info Cards -->
        <div class="info-grid">
            <div class="info-card">
                <h3>🎯 核心設計</h3>
                <p>基於遊戲設計文檔，建立完整的類型安全系統，確保代碼質量和可維護性。</p>
            </div>
            <div class="info-card">
                <h3>🔧 模塊化</h3>
                <p>按功能分離類型定義，便於管理和擴展，支持漸進式開發。</p>
            </div>
            <div class="info-card">
                <h3>🛡️ 類型安全</h3>
                <p>提供運行時類型檢查和驗證工具，防止類型錯誤和數據異常。</p>
            </div>
            <div class="info-card">
                <h3>📚 文檔完善</h3>
                <p>詳細的類型註釋和使用示例，降低學習成本，提高開發效率。</p>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            classDiagram: {
                useMaxWidth: true
            }
        });

        // Tab 切換功能
        function showTab(tabName) {
            // 隱藏所有 tab content
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有 tab 的 active 狀態
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 顯示選中的 tab content
            document.getElementById(tabName).classList.add('active');

            // 添加選中 tab 的 active 狀態
            event.target.classList.add('active');

            // 重新渲染 Mermaid 圖表
            setTimeout(() => {
                mermaid.init();
            }, 100);
        }

        // 頁面載入完成後的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加一些互動效果
            const infoCards = document.querySelectorAll('.info-card');
            infoCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                    this.style.boxShadow = '0 15px 40px rgba(0, 0, 0, 0.2)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = 'none';
                });
            });
        });
    </script>
</body>
</html>
