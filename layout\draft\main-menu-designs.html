<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主界面 - 3種設計風格</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #1a1a1a;
            color: white;
            overflow-x: hidden;
        }

        .design-container {
            max-width: 375px;
            margin: 0 auto;
            min-height: 100vh;
            position: relative;
            border: 2px solid #333;
            margin-bottom: 40px;
        }

        .design-title {
            position: absolute;
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: #000;
            padding: 5px 15px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 1000;
        }

        /* ========== 賽博朋克風格 ========== */
        .cyberpunk {
            background: linear-gradient(135deg, #0a0014 0%, #1a0033 50%, #001a33 100%);
        }

        .cyberpunk .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 20px;
            background: linear-gradient(90deg, #00f5ff20, transparent, #ff006e20);
            border-bottom: 1px solid #00f5ff;
            font-size: 12px;
            font-family: 'Courier New', monospace;
        }

        .cyberpunk .resource {
            background: linear-gradient(45deg, #00f5ff40, #ff006e40);
            border: 1px solid #00f5ff;
            padding: 3px 8px;
            border-radius: 0;
            box-shadow: 0 0 10px #00f5ff80;
            position: relative;
        }

        .cyberpunk .resource:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, #00f5ff40, transparent);
            animation: scan 2s infinite;
        }

        .cyberpunk .main-content {
            flex: 1;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .cyberpunk .stage-header {
            text-align: center;
            margin-bottom: 10px;
        }

        .cyberpunk .stage-title {
            font-size: 24px;
            font-weight: bold;
            color: #00f5ff;
            text-shadow: 0 0 20px #00f5ff;
            font-family: 'Courier New', monospace;
            animation: glitch 3s infinite;
        }

        .cyberpunk .battle-preview {
            background: linear-gradient(45deg, #00f5ff10, #ff006e10);
            border: 2px solid #00f5ff;
            border-radius: 0;
            padding: 30px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .cyberpunk .battle-preview:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, #00f5ff40, transparent);
            animation: holographic 3s infinite;
        }

        .cyberpunk .start-battle-btn {
            background: linear-gradient(135deg, #00f5ff, #ff006e);
            border: 2px solid #00f5ff;
            color: black;
            padding: 16px 40px;
            border-radius: 0;
            font-size: 18px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            text-transform: uppercase;
            box-shadow: 0 0 20px #00f5ff;
            transition: all 0.3s ease;
        }

        .cyberpunk .start-battle-btn:hover {
            box-shadow: 0 0 40px #ff006e;
            transform: scale(1.05);
        }

        .cyberpunk .nav-tabs {
            display: flex;
            justify-content: space-around;
            background: linear-gradient(90deg, #00f5ff20, #ff006e20);
            border-top: 2px solid #00f5ff;
            padding: 15px 0;
        }

        .cyberpunk .nav-tab {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px;
            border: 1px solid transparent;
            transition: all 0.3s ease;
            font-family: 'Courier New', monospace;
        }

        .cyberpunk .nav-tab:hover {
            border: 1px solid #00f5ff;
            box-shadow: 0 0 15px #00f5ff80;
        }

        .cyberpunk .nav-tab.active {
            border: 1px solid #ff006e;
            color: #ff006e;
            box-shadow: 0 0 15px #ff006e80;
        }

        /* ========== 幻想魔法風格 ========== */
        .fantasy {
            background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #581c87 100%);
            position: relative;
        }

        .fantasy:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 30%, #fbbf2450 2px, transparent 2px),
                radial-gradient(circle at 70% 60%, #7c3aed50 1px, transparent 1px),
                radial-gradient(circle at 40% 80%, #fbbf2450 1px, transparent 1px);
            animation: twinkle 4s infinite;
        }

        .fantasy .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 20px;
            background: rgba(123, 58, 237, 0.3);
            border-bottom: 2px solid #fbbf24;
            border-image: linear-gradient(90deg, #fbbf24, #7c3aed, #fbbf24) 1;
        }

        .fantasy .resource {
            background: linear-gradient(45deg, #7c3aed80, #fbbf2480);
            border: 2px solid #fbbf24;
            border-radius: 20px;
            padding: 4px 10px;
            box-shadow: 0 0 15px #fbbf2480;
            position: relative;
        }

        .fantasy .resource:before {
            content: '✨';
            position: absolute;
            top: -5px;
            right: -5px;
            font-size: 10px;
            animation: sparkle 2s infinite;
        }

        .fantasy .main-content {
            flex: 1;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
            z-index: 2;
            position: relative;
        }

        .fantasy .stage-title {
            font-size: 24px;
            font-weight: bold;
            color: #fbbf24;
            text-shadow: 2px 2px 8px #7c3aed;
            font-family: serif;
            text-align: center;
            margin-bottom: 15px;
        }

        .fantasy .battle-preview {
            background: linear-gradient(45deg, rgba(123, 58, 237, 0.2), rgba(251, 191, 36, 0.1));
            border: 3px solid #fbbf24;
            border-radius: 20px;
            padding: 30px 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            position: relative;
            box-shadow: 0 10px 30px rgba(123, 58, 237, 0.4);
        }

        .fantasy .battle-preview:before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 40px;
            background: radial-gradient(circle, #fbbf24, #7c3aed);
            border-radius: 50%;
            box-shadow: 0 0 20px #fbbf24;
        }

        .fantasy .start-battle-btn {
            background: linear-gradient(135deg, #7c3aed, #fbbf24);
            border: 3px solid #fbbf24;
            color: white;
            padding: 16px 40px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            font-family: serif;
            box-shadow: 0 8px 20px rgba(123, 58, 237, 0.5);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .fantasy .start-battle-btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, #ffffff50, transparent);
            animation: magicShine 2s infinite;
        }

        .fantasy .nav-tabs {
            display: flex;
            justify-content: space-around;
            background: linear-gradient(90deg, #7c3aed80, #fbbf2480, #7c3aed80);
            border-top: 3px solid #fbbf24;
            padding: 15px 0;
            border-radius: 20px 20px 0 0;
        }

        .fantasy .nav-tab {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 10px;
            border-radius: 15px;
            transition: all 0.3s ease;
            position: relative;
        }

        .fantasy .nav-tab:hover {
            background: rgba(251, 191, 36, 0.3);
            box-shadow: 0 0 15px #fbbf2480;
        }

        .fantasy .nav-tab.active {
            background: linear-gradient(45deg, #7c3aed80, #fbbf2480);
            color: #fbbf24;
            box-shadow: 0 0 20px #fbbf24;
        }

        /* ========== 極簡現代風格 ========== */
        .minimal {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #1f2937;
        }

        .minimal .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            background: #ffffff;
            border-bottom: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .minimal .resource {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 20px;
            padding: 6px 12px;
            font-size: 14px;
            color: #374151;
            font-weight: 500;
        }

        .minimal .main-content {
            flex: 1;
            padding: 30px 20px;
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .minimal .stage-title {
            font-size: 28px;
            font-weight: 300;
            color: #1f2937;
            text-align: center;
            margin-bottom: 20px;
            letter-spacing: -0.5px;
        }

        .minimal .battle-preview {
            background: #ffffff;
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid #f3f4f6;
        }

        .minimal .preview-content h3 {
            font-size: 16px;
            font-weight: 400;
            color: #6b7280;
            margin-bottom: 20px;
        }

        .minimal .start-battle-btn {
            background: #3b82f6;
            border: none;
            color: white;
            padding: 16px 40px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .minimal .start-battle-btn:hover {
            background: #2563eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .minimal .team-status {
            background: #ffffff;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #f3f4f6;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .minimal .nav-tabs {
            display: flex;
            justify-content: space-around;
            background: #ffffff;
            border-top: 1px solid #e5e7eb;
            padding: 20px 0;
        }

        .minimal .nav-tab {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            padding: 12px;
            border-radius: 8px;
            transition: all 0.2s ease;
            color: #6b7280;
        }

        .minimal .nav-tab:hover {
            background: #f9fafb;
            color: #374151;
        }

        .minimal .nav-tab.active {
            background: #eff6ff;
            color: #3b82f6;
        }

        .minimal .nav-icon {
            font-size: 20px;
        }

        .minimal .nav-label {
            font-size: 12px;
            font-weight: 500;
        }

        /* 動畫效果 */
        @keyframes scan {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        @keyframes glitch {
            0%, 100% { transform: translateX(0); }
            10% { transform: translateX(-2px); }
            20% { transform: translateX(2px); }
            30% { transform: translateX(-1px); }
        }

        @keyframes holographic {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        @keyframes twinkle {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes sparkle {
            0%, 100% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.2); }
        }

        @keyframes magicShine {
            0% { left: -100%; }
            100% { left: 100%; }
        }
    </style>
</head>
<body>
    <!-- 賽博朋克風格 -->
    <div class="design-container cyberpunk">
        <div class="design-title">🔮 賽博朋克風格</div>
        
        <div class="status-bar">
            <div class="status-left">
                <span>⚡ 95%</span>
                <span>15:30</span>
            </div>
            <div class="status-right">
                <div class="resource">
                    <span>💎</span>
                    <span>1,250</span>
                </div>
                <div class="resource">
                    <span>💰</span>
                    <span>5,600</span>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="stage-header">
                <h1 class="stage-title">STAGE_05::FOREST.exe</h1>
                <div class="stage-progress" style="color: #00f5ff;">STATUS: 3/3 ⭐⭐⭐</div>
            </div>

            <div class="battle-preview">
                <div class="preview-content">
                    <h3 style="color: #ff006e; margin-bottom: 15px;">ENEMY.TARGETS_DETECTED</h3>
                    <div class="enemy-preview" style="display: flex; gap: 10px; justify-content: center; margin-bottom: 20px;">
                        <div style="width: 40px; height: 40px; background: #ff006e; display: flex; align-items: center; justify-content: center; border: 2px solid #00f5ff;">🐺</div>
                        <div style="width: 40px; height: 40px; background: #ff006e; display: flex; align-items: center; justify-content: center; border: 2px solid #00f5ff;">🧌</div>
                        <div style="width: 40px; height: 40px; background: #ff006e; display: flex; align-items: center; justify-content: center; border: 2px solid #00f5ff;">🕷️</div>
                    </div>
                    <div style="color: #00f5ff; margin-bottom: 20px; font-family: 'Courier New', monospace;">
                        POWER_LEVEL: 980
                    </div>
                    <button class="start-battle-btn">
                        >>> ENGAGE <<<
                    </button>
                </div>
            </div>

            <div class="team-status" style="background: linear-gradient(45deg, #00f5ff10, #ff006e10); border: 2px solid #00f5ff; border-radius: 0; padding: 15px;">
                <div style="font-size: 16px; margin-bottom: 12px; font-weight: bold; color: #00f5ff; font-family: 'Courier New', monospace;">TEAM.POWER: 1,250</div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="margin-right: 8px;">UNITS:</span>
                    <div style="width: 35px; height: 45px; background: linear-gradient(145deg, #ff006e, #00f5ff); display: flex; align-items: center; justify-content: center; border: 1px solid #00f5ff;">🔥</div>
                    <div style="width: 35px; height: 45px; background: linear-gradient(145deg, #ff006e, #00f5ff); display: flex; align-items: center; justify-content: center; border: 1px solid #00f5ff;">🏹</div>
                    <div style="width: 35px; height: 45px; background: linear-gradient(145deg, #ff006e, #00f5ff); display: flex; align-items: center; justify-content: center; border: 1px solid #00f5ff;">🪓</div>
                    <span style="margin-left: auto; color: #00f5ff; font-family: 'Courier New', monospace;">3/6</span>
                </div>
            </div>
        </div>

        <div class="nav-tabs">
            <div class="nav-tab active">
                <div class="nav-icon">⚔️</div>
                <div class="nav-label">BATTLE</div>
            </div>
            <div class="nav-tab">
                <div class="nav-icon">🎴</div>
                <div class="nav-label">GACHA</div>
            </div>
            <div class="nav-tab">
                <div class="nav-icon">👥</div>
                <div class="nav-label">SQUAD</div>
            </div>
            <div class="nav-tab">
                <div class="nav-icon">📊</div>
                <div class="nav-label">DATA</div>
            </div>
            <div class="nav-tab">
                <div class="nav-icon">⚙️</div>
                <div class="nav-label">CONFIG</div>
            </div>
        </div>
    </div>

    <!-- 幻想魔法風格 -->
    <div class="design-container fantasy">
        <div class="design-title">✨ 幻想魔法風格</div>
        
        <div class="status-bar">
            <div class="status-left">
                <span>🔋 95%</span>
                <span>15:30</span>
            </div>
            <div class="status-right">
                <div class="resource">
                    <span>💎</span>
                    <span>1,250</span>
                </div>
                <div class="resource">
                    <span>💰</span>
                    <span>5,600</span>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="stage-header">
                <h1 class="stage-title">🏰 第五章 魔法森林</h1>
                <div class="stage-progress" style="color: #fbbf24; text-align: center;">征服進度: 3/3 ⭐⭐⭐</div>
            </div>

            <div class="battle-preview">
                <div class="preview-content">
                    <h3 style="color: #fbbf24; margin-bottom: 15px;">古老的敵人甦醒</h3>
                    <div class="enemy-preview" style="display: flex; gap: 15px; justify-content: center; margin-bottom: 20px;">
                        <div style="width: 50px; height: 50px; background: radial-gradient(circle, #7c3aed, #fbbf24); display: flex; align-items: center; justify-content: center; border-radius: 50%; border: 3px solid #fbbf24; box-shadow: 0 0 15px #7c3aed;">🐺</div>
                        <div style="width: 50px; height: 50px; background: radial-gradient(circle, #7c3aed, #fbbf24); display: flex; align-items: center; justify-content: center; border-radius: 50%; border: 3px solid #fbbf24; box-shadow: 0 0 15px #7c3aed;">🧌</div>
                        <div style="width: 50px; height: 50px; background: radial-gradient(circle, #7c3aed, #fbbf24); display: flex; align-items: center; justify-content: center; border-radius: 50%; border: 3px solid #fbbf24; box-shadow: 0 0 15px #7c3aed;">🕷️</div>
                    </div>
                    <div style="color: #fbbf24; margin-bottom: 20px; font-family: serif;">
                        ⚡ 魔力值: 980 ⚡
                    </div>
                    <button class="start-battle-btn">
                        🔮 開始魔法戰鬥 🔮
                    </button>
                </div>
            </div>

            <div class="team-status" style="background: linear-gradient(45deg, rgba(123, 58, 237, 0.2), rgba(251, 191, 36, 0.1)); border: 3px solid #fbbf24; border-radius: 15px; padding: 20px;">
                <div style="font-size: 16px; margin-bottom: 12px; font-weight: bold; color: #fbbf24; font-family: serif;">🏆 隊伍魔力: 1,250</div>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="margin-right: 8px; color: #fbbf24;">⭐</span>
                    <div style="width: 40px; height: 50px; background: linear-gradient(145deg, #7c3aed, #fbbf24); display: flex; align-items: center; justify-content: center; border-radius: 10px; border: 2px solid #fbbf24; box-shadow: 0 0 10px #7c3aed;">🔥</div>
                    <div style="width: 40px; height: 50px; background: linear-gradient(145deg, #7c3aed, #fbbf24); display: flex; align-items: center; justify-content: center; border-radius: 10px; border: 2px solid #fbbf24; box-shadow: 0 0 10px #7c3aed;">🏹</div>
                    <div style="width: 40px; height: 50px; background: linear-gradient(145deg, #7c3aed, #fbbf24); display: flex; align-items: center; justify-content: center; border-radius: 10px; border: 2px solid #fbbf24; box-shadow: 0 0 10px #7c3aed;">🪓</div>
                    <span style="margin-left: auto; color: #fbbf24; font-family: serif;">3/6</span>
                </div>
            </div>
        </div>

        <div class="nav-tabs">
            <div class="nav-tab active">
                <div class="nav-icon">⚔️</div>
                <div class="nav-label">戰鬥</div>
            </div>
            <div class="nav-tab">
                <div class="nav-icon">🎴</div>
                <div class="nav-label">召喚</div>
            </div>
            <div class="nav-tab">
                <div class="nav-icon">👥</div>
                <div class="nav-label">隊伍</div>
            </div>
            <div class="nav-tab">
                <div class="nav-icon">📊</div>
                <div class="nav-label">法典</div>
            </div>
            <div class="nav-tab">
                <div class="nav-icon">⚙️</div>
                <div class="nav-label">魔法</div>
            </div>
        </div>
    </div>

    <!-- 極簡現代風格 -->
    <div class="design-container minimal">
        <div class="design-title" style="color: #1f2937;">🎯 極簡現代風格</div>
        
        <div class="status-bar">
            <div class="status-left">
                <span>🔋 95%</span>
                <span>15:30</span>
            </div>
            <div class="status-right">
                <div class="resource">
                    <span>💎</span>
                    <span>1,250</span>
                </div>
                <div class="resource">
                    <span>💰</span>
                    <span>5,600</span>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="stage-header">
                <h1 class="stage-title">Stage 5</h1>
                <div class="stage-progress" style="color: #6b7280; text-align: center; font-size: 14px;">Forest Depths • Progress: 3/3</div>
            </div>

            <div class="battle-preview">
                <div class="preview-content">
                    <h3>Upcoming Battle</h3>
                    <div class="enemy-preview" style="display: flex; gap: 12px; justify-content: center; margin: 20px 0;">
                        <div style="width: 48px; height: 48px; background: #f3f4f6; display: flex; align-items: center; justify-content: center; border-radius: 8px; border: 1px solid #d1d5db;">🐺</div>
                        <div style="width: 48px; height: 48px; background: #f3f4f6; display: flex; align-items: center; justify-content: center; border-radius: 8px; border: 1px solid #d1d5db;">🧌</div>
                        <div style="width: 48px; height: 48px; background: #f3f4f6; display: flex; align-items: center; justify-content: center; border-radius: 8px; border: 1px solid #d1d5db;">🕷️</div>
                    </div>
                    <div style="color: #6b7280; margin-bottom: 30px; font-size: 14px;">
                        Enemy Power: 980
                    </div>
                    <button class="start-battle-btn">
                        Start Battle
                    </button>
                </div>
            </div>

            <div class="team-status">
                <div style="font-size: 16px; margin-bottom: 15px; font-weight: 500; color: #374151;">Team Power: 1,250</div>
                <div style="display: flex; align-items: center; gap: 12px;">
                    <span style="margin-right: 8px; color: #6b7280;">Team</span>
                    <div style="width: 40px; height: 50px; background: linear-gradient(145deg, #3b82f6, #1d4ed8); display: flex; align-items: center; justify-content: center; border-radius: 6px; color: white;">🔥</div>
                    <div style="width: 40px; height: 50px; background: linear-gradient(145deg, #10b981, #059669); display: flex; align-items: center; justify-content: center; border-radius: 6px; color: white;">🏹</div>
                    <div style="width: 40px; height: 50px; background: linear-gradient(145deg, #f59e0b, #d97706); display: flex; align-items: center; justify-content: center; border-radius: 6px; color: white;">🪓</div>
                    <span style="margin-left: auto; color: #6b7280; font-size: 14px;">3/6</span>
                </div>
            </div>
        </div>

        <div class="nav-tabs">
            <div class="nav-tab active">
                <div class="nav-icon">⚔️</div>
                <div class="nav-label">Battle</div>
            </div>
            <div class="nav-tab">
                <div class="nav-icon">🎴</div>
                <div class="nav-label">Cards</div>
            </div>
            <div class="nav-tab">
                <div class="nav-icon">👥</div>
                <div class="nav-label">Team</div>
            </div>
            <div class="nav-tab">
                <div class="nav-icon">📊</div>
                <div class="nav-label">Stats</div>
            </div>
            <div class="nav-tab">
                <div class="nav-icon">⚙️</div>
                <div class="nav-label">Settings</div>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                const container = this.closest('.design-container');
                container.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        document.querySelectorAll('.start-battle-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const originalText = this.textContent;
                this.textContent = this.closest('.cyberpunk') ? '>>> LOADING <<<' : 
                                  this.closest('.fantasy') ? '🔮 施法中... 🔮' : 
                                  'Loading...';
                this.disabled = true;
                
                setTimeout(() => {
                    this.textContent = originalText;
                    this.disabled = false;
                }, 2000);
            });
        });
    </script>
</body>
</html>