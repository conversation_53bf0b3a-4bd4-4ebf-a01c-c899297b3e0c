# 🃏 PetingGame 類型系統

這個目錄包含了整個遊戲的TypeScript類型定義系統，為遊戲的各個模塊提供強類型支持。

## 📁 文件結構

```
types/
├── index.ts          # 核心類型定義和統一導出
├── config.ts         # 配置系統類型
├── battle.ts         # 戰鬥系統類型
├── ui.ts            # UI組件類型
├── gacha.ts         # 抽卡系統類型
├── utils.ts         # 類型工具函數
├── constants.ts     # 遊戲常量定義
└── README.md        # 本文件
```

## 🎯 核心類型概覽

### 基礎數據類型

- **Card**: 卡牌基礎數據結構
- **BattleCard**: 戰鬥中的卡牌狀態
- **Team**: 隊伍配置
- **PlayerData**: 玩家數據
- **BattleResult**: 戰鬥結果

### 枚舉類型

- **CardRarity**: 卡牌稀有度 (1-5星)
- **CardRace**: 卡牌種族 (人族、精靈族等)
- **BattleTag**: 戰鬥標籤 (戰士、法師等)
- **ElementTag**: 元素標籤 (火、水、土等)
- **SpecialTag**: 特殊標籤 (精英、首領等)

## 🔧 使用方法

### 基本導入

```typescript
// 導入核心類型
import { Card, BattleCard, CardRarity, CardRace } from '@/types';

// 導入特定模塊類型
import { BattleManager, AIDecision } from '@/types/battle';
import { GachaManager, GachaResult } from '@/types/gacha';
import { ThemeColors, ButtonComponentProps } from '@/types/ui';
```

### 類型守衛使用

```typescript
import { isCard, isBattleCard, isValidRarity } from '@/types/utils';

// 運行時類型檢查
function processCard(data: unknown) {
  if (isCard(data)) {
    // data 現在被確認為 Card 類型
    console.log(data.name, data.rarity);
  }
}

// 驗證用戶輸入
function setCardRarity(input: string) {
  const rarity = parseRarity(input);
  if (rarity !== null) {
    // rarity 是有效的 CardRarity
    return rarity;
  }
  throw new Error('Invalid rarity');
}
```

### 常量使用

```typescript
import { RARITY_CONFIG, RACE_CONFIG, GAME_CONFIG } from '@/types/constants';

// 獲取稀有度配置
const rarityInfo = RARITY_CONFIG[CardRarity.LEGENDARY];
console.log(rarityInfo.name); // "傳說"
console.log(rarityInfo.color); // "#FFD700"

// 使用遊戲配置
const maxTeamSize = GAME_CONFIG.MAX_TEAM_SIZE; // 6
```

## 🎨 UI組件類型

### 組件Props定義

```typescript
import { CardComponentProps, ButtonComponentProps } from '@/types/ui';

// 卡牌組件
const CardDisplay: React.FC<CardComponentProps> = ({ 
  card, 
  size = 'normal',
  onPress 
}) => {
  return (
    <TouchableOpacity onPress={() => onPress?.(card)}>
      {/* 卡牌內容 */}
    </TouchableOpacity>
  );
};

// 按鈕組件
const GameButton: React.FC<ButtonComponentProps> = ({
  title,
  variant = 'primary',
  onPress
}) => {
  return (
    <TouchableOpacity onPress={onPress}>
      <Text>{title}</Text>
    </TouchableOpacity>
  );
};
```

### 主題系統

```typescript
import { Theme, ThemeColors } from '@/types/ui';

const lightTheme: Theme = {
  colors: {
    primary: '#4A90E2',
    background: '#FFFFFF',
    text: '#000000',
    // ... 其他顏色
  },
  sizes: {
    xs: 4,
    sm: 8,
    md: 16,
    // ... 其他尺寸
  },
  isDark: false
};
```

## ⚔️ 戰鬥系統類型

### 戰鬥管理

```typescript
import { BattleManager, Battle, BattleAction } from '@/types/battle';

class GameBattleManager implements BattleManager {
  async initializeBattle(stageId: string, playerTeam: Card[]): Promise<Battle> {
    // 初始化戰鬥邏輯
    return {
      id: generateId(),
      stageId,
      state: BattleState.PREPARING,
      playerTeam: playerTeam.map(card => toBattleCard(card)),
      // ... 其他屬性
    };
  }
  
  executeAction(action: BattleAction): void {
    // 執行戰鬥行動
  }
}
```

### AI決策

```typescript
import { AIDecision, AIEvaluationContext } from '@/types/battle';

function makeAIDecision(context: AIEvaluationContext): AIDecision {
  return {
    cardId: context.aiCard.id,
    actionType: BattleActionType.NORMAL_ATTACK,
    targetIds: [selectTarget(context.enemyCards)],
    confidence: 0.8,
    reasoning: 'Target has lowest health',
    alternativeActions: []
  };
}
```

## 🎴 抽卡系統類型

### 抽卡執行

```typescript
import { GachaManager, GachaRequest, GachaResult } from '@/types/gacha';

class GameGachaManager implements GachaManager {
  async performDraw(request: GachaRequest): Promise<GachaResult> {
    // 執行抽卡邏輯
    return {
      requestId: generateId(),
      success: true,
      cards: [], // 抽到的卡牌
      totalCost: request.cost,
      currencyType: request.currencyType,
      // ... 其他結果
    };
  }
}
```

## 🔧 配置系統類型

### CSV配置載入

```typescript
import { CardConfig, SkillConfig, ConfigManager } from '@/types/config';

class GameConfigManager implements ConfigManager {
  cards = new Map<string, CardConfig>();
  skills = new Map<string, SkillConfig>();
  
  async loadConfigs(): Promise<void> {
    // 載入CSV配置文件
    const cardData = await loadCSV('CardConfig.csv');
    cardData.forEach(config => {
      this.cards.set(config.id, config);
    });
  }
  
  getCardConfig(id: string): CardConfig | undefined {
    return this.cards.get(id);
  }
}
```

## 🛡️ 類型安全最佳實踐

### 1. 使用類型守衛

```typescript
// 好的做法
if (isCard(data)) {
  // TypeScript 知道 data 是 Card 類型
  processCard(data);
}

// 避免的做法
const card = data as Card; // 不安全的類型斷言
```

### 2. 使用工具函數

```typescript
// 好的做法
const rarity = parseRarity(userInput);
if (rarity !== null) {
  // 安全地使用 rarity
}

// 避免的做法
const rarity = parseInt(userInput) as CardRarity; // 可能無效
```

### 3. 驗證必要屬性

```typescript
// 好的做法
if (hasRequiredProperties(obj, ['id', 'name', 'stats.attack'])) {
  // 確保對象具有必要屬性
}
```

## 🔄 類型擴展

如果需要添加新的類型，請遵循以下原則：

1. **在適當的文件中添加類型定義**
2. **更新相關的常量配置**
3. **添加對應的類型守衛函數**
4. **更新導出語句**
5. **添加使用示例到README**

### 示例：添加新的卡牌類型

```typescript
// 1. 在 index.ts 中添加枚舉
export enum NewCardType {
  TYPE_A = 'TypeA',
  TYPE_B = 'TypeB'
}

// 2. 在 constants.ts 中添加配置
export const NEW_TYPE_CONFIG = {
  [NewCardType.TYPE_A]: { name: '類型A', color: '#FF0000' },
  [NewCardType.TYPE_B]: { name: '類型B', color: '#00FF00' },
} as const;

// 3. 在 utils.ts 中添加類型守衛
export function isValidNewCardType(value: any): value is NewCardType {
  return Object.values(NewCardType).includes(value);
}
```

## 📚 相關文檔

- [遊戲設計文檔](../docs/game_design.md)
- [React Native TypeScript 指南](https://reactnative.dev/docs/typescript)
- [TypeScript 官方文檔](https://www.typescriptlang.org/docs/)
