# 手繪藝術風格卡牌佈局指南

## 設計概念

手繪藝術風格以溫暖的米色調和自然的手工質感為特色，營造出親切、溫馨的遊戲體驗。

### 色彩規範

#### 主要色彩
- **背景色**: `#f7f3e9` → `#e8dcc0` → `#d4c5a0` (漸層)
- **卡片底色**: `#f9f7f4` → `#f0ede6` (漸層)
- **邊框色**: `#8b4513` (深棕色)
- **文字色**: `#3c2415` (深褐色)

#### 強調色彩
- **等級標籤**: `#f4a261` (橙黃色)
- **攻擊力**: `#d2691e` (橙棕色) 
- **速度**: `#228b22` (森林綠)
- **種族標籤**: `#f4a261` (橙黃色)
- **稀有度**: `#f4a261` (橙黃色)

## 佈局結構

### 卡片基礎規格
```css
寬度: 100px
高度: 140px
邊框: 3px solid #8b4513
圓角: 8px
陰影: 0 6px 20px rgba(139, 69, 19, 0.3)
```

### 組件層級結構

#### 1. 卡片頂部 (Card Header)
- **位置**: 上方，padding: 6px
- **內容**: 等級標籤 + 生命值
- **字體**: Times New Roman, 10px, bold
- **等級標籤特效**: 
  - 背景: `#f4a261`
  - 圓角: 12px
  - 旋轉: -5度
  - 陰影: 2px 2px 0px rgba(139, 69, 19, 0.3)

#### 2. 圖標區域 (Card Icon)
- **位置**: 中央上方，margin: 8px 0
- **大小**: 28px
- **特效**: 
  - sepia(0.3) contrast(1.2)
  - 旋轉: -2度
  - 上下浮動動畫 (4秒循環)

#### 3. 卡片名稱 (Card Name)
- **位置**: 中央，padding: 4px
- **字體**: Times New Roman, 11px, bold
- **特效**:
  - 旋轉: 1度
  - 下劃線 (rgba(139, 69, 19, 0.3))

#### 4. 屬性數值 (Card Stats)
- **位置**: 中央下方，padding: 6px
- **佈局**: 左右分佈 (攻擊力 vs 速度)
- **字體**: Times New Roman, 10px, bold
- **特效**:
  - 攻擊力: 旋轉 -1度
  - 速度: 旋轉 1度

#### 5. 行動條 (Action Bar)
- **位置**: margin: 4px 6px
- **高度**: 6px
- **樣式**: 
  - 背景: rgba(139, 69, 19, 0.2)
  - 邊框: 1px dashed #8b4513
  - 進度條: 漸層 #228b22 → #f4a261
  - 斜紋特效覆蓋層

#### 6. 卡片底部 (Card Footer)
- **位置**: 底部，padding: 4px 6px
- **內容**: 種族標籤 + 稀有度星級
- **種族標籤特效**:
  - 旋轉: -3度
  - 圓角: 12px
  - 陰影: 1px 1px 0px rgba(139, 69, 19, 0.3)
- **星級特效**:
  - 旋轉: 2度
  - 陰影: drop-shadow(1px 1px 1px rgba(139, 69, 19, 0.3))

## 視覺特效

### 質感層次

#### 紙張質感 (:before)
```css
background: 
  radial-gradient(circle at 20% 30%, rgba(139, 69, 19, 0.1) 2px, transparent 2px),
  radial-gradient(circle at 70% 60%, rgba(139, 69, 19, 0.05) 1px, transparent 1px),
  radial-gradient(circle at 40% 80%, rgba(139, 69, 19, 0.08) 1px, transparent 1px);
background-size: 20px 20px, 15px 15px, 25px 25px;
```

#### 虛線內框 (:after)
```css
border: 2px dashed #8b4513;
opacity: 0.3;
inset: 8px;
```

### 互動效果

#### Hover 狀態
```css
transform: translateY(-4px) rotate(2deg);
box-shadow: 0 12px 30px rgba(139, 69, 19, 0.4);
filter: drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.2));
```

#### 點擊效果
```css
transform: translateY(-4px) rotate(5deg) scale(1.05);
filter: drop-shadow(6px 6px 12px rgba(0, 0, 0, 0.3));
duration: 300ms
```

### 動畫規範

#### 浮動動畫 (handDrawnBob)
```css
@keyframes handDrawnBob {
  0%, 100% { transform: translateY(0) rotate(-2deg); }
  50% { transform: translateY(-2px) rotate(0deg); }
}
duration: 4s infinite ease-in-out
```

#### 行動條閃爍 (滿格時)
- 觸發條件: action-progress >= 100%
- 效果: filter brightness 增強
- 間隔: 2秒檢查一次

## 響應式適配

### 小螢幕優化 (≤360px)
```css
卡片尺寸: 85px × 120px
圖標大小: 24px
字體比例: 保持相對大小
```

## React Native 實現指南

### 組件結構
```tsx
<View style={styles.card}>
  <View style={styles.cardContent}>
    <View style={styles.cardHeader}>
      <Text style={styles.cardLevel}>L{level}</Text>
      <Text style={styles.health}>❤️ {health}</Text>
    </View>
    
    <Text style={styles.cardIcon}>{icon}</Text>
    <Text style={styles.cardName}>{name}</Text>
    
    <View style={styles.cardStats}>
      <Text style={[styles.stat, styles.statAttack]}>⚔️ {attack}</Text>
      <Text style={[styles.stat, styles.statSpeed]}>⚡ {speed}</Text>
    </View>
    
    <View style={styles.actionBar}>
      <View style={[styles.actionProgress, {width: `${progress}%`}]} />
    </View>
    
    <View style={styles.cardFooter}>
      <Text style={styles.raceTag}>{race}</Text>
      <Text style={styles.rarityStars}>{'⭐'.repeat(rarity)}</Text>
    </View>
  </View>
</View>
```

### 關鍵樣式實現
- 使用 `transform` 屬性實現旋轉效果
- 利用 `shadowColor` 和 `elevation` 模擬陰影
- 通過 `Animated` API 實現浮動和閃爍動畫
- 使用 `LinearGradient` 組件創建漸層效果

此風格適合營造溫馨、親近的遊戲氛圍，特別適用於休閒卡牌遊戲。