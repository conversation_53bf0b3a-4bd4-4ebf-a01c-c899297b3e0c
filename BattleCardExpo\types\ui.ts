/**
 * 🎨 UI系統類型定義
 * 
 * 定義所有UI組件和界面相關的類型和接口
 * 基於 docs/game_design.md 中的手機優化界面設計
 */

import { Card, BattleCard, Team, PlayerData, BattleResult } from './index';
import { ViewStyle, TextStyle, ImageStyle } from 'react-native';

// ==================== 主題系統接口 ====================

/**
 * 主題顏色接口
 */
export interface ThemeColors {
  // 基礎顏色
  primary: string;                      // 主色調
  secondary: string;                    // 次要色調
  background: string;                   // 背景色
  surface: string;                      // 表面色
  text: string;                         // 文字色
  textSecondary: string;                // 次要文字色
  border: string;                       // 邊框色
  shadow: string;                       // 陰影色
  
  // 功能顏色
  success: string;                      // 成功色 (綠色)
  warning: string;                      // 警告色 (黃色)
  error: string;                        // 錯誤色 (紅色)
  info: string;                         // 信息色 (藍色)
  
  // 種族顏色
  human: string;                        // 人族 - 藍色
  elf: string;                          // 精靈族 - 綠色
  orc: string;                          // 獸人族 - 橙色
  dragon: string;                       // 龍族 - 紅色
  angel: string;                        // 天使族 - 紫色
  demon: string;                        // 惡魔族 - 暗紅色
  
  // 稀有度顏色
  common: string;                       // 普通 - 白色
  rare: string;                         // 稀有 - 藍色
  epic: string;                         // 史詩 - 紫色
  legendary: string;                    // 傳說 - 金色
  mythic: string;                       // 神話 - 七彩色
  
  // 元素顏色
  fire: string;                         // 火 - 紅色
  water: string;                        // 水 - 藍色
  earth: string;                        // 土 - 棕色
  air: string;                          // 風 - 青色
  light: string;                        // 光 - 白色
  dark: string;                         // 暗 - 黑色
}

/**
 * 主題尺寸接口
 */
export interface ThemeSizes {
  // 間距
  xs: number;                           // 4px
  sm: number;                           // 8px
  md: number;                           // 16px
  lg: number;                           // 24px
  xl: number;                           // 32px
  xxl: number;                          // 48px
  
  // 字體大小
  fontXs: number;                       // 12px
  fontSm: number;                       // 14px
  fontMd: number;                       // 16px
  fontLg: number;                       // 18px
  fontXl: number;                       // 20px
  fontXxl: number;                      // 24px
  fontTitle: number;                    // 32px
  
  // 圓角
  radiusXs: number;                     // 2px
  radiusSm: number;                     // 4px
  radiusMd: number;                     // 8px
  radiusLg: number;                     // 12px
  radiusXl: number;                     // 16px
  radiusFull: number;                   // 9999px
  
  // 觸控區域
  touchTarget: number;                  // 48px 最小觸控區域
  buttonHeight: number;                 // 44px 按鈕高度
  inputHeight: number;                  // 40px 輸入框高度
  
  // 卡牌尺寸
  cardWidth: number;                    // 120px
  cardHeight: number;                   // 160px
  cardMiniWidth: number;                // 80px
  cardMiniHeight: number;               // 100px
}

/**
 * 主題接口
 */
export interface Theme {
  colors: ThemeColors;                  // 顏色配置
  sizes: ThemeSizes;                    // 尺寸配置
  isDark: boolean;                      // 是否暗色主題
}

// ==================== 組件Props接口 ====================

/**
 * 基礎組件Props
 */
export interface BaseComponentProps {
  style?: ViewStyle;                    // 樣式
  testID?: string;                      // 測試ID
  accessible?: boolean;                 // 無障礙
  accessibilityLabel?: string;          // 無障礙標籤
}

/**
 * 卡牌組件Props
 */
export interface CardComponentProps extends BaseComponentProps {
  card: Card | BattleCard;              // 卡牌數據
  size?: 'mini' | 'normal' | 'large';   // 尺寸
  showLevel?: boolean;                  // 顯示等級
  showStats?: boolean;                  // 顯示屬性
  showActionBar?: boolean;              // 顯示行動條
  isSelected?: boolean;                 // 是否選中
  isDisabled?: boolean;                 // 是否禁用
  onPress?: (card: Card | BattleCard) => void; // 點擊回調
  onLongPress?: (card: Card | BattleCard) => void; // 長按回調
  onDoublePress?: (card: Card | BattleCard) => void; // 雙擊回調
}

/**
 * 按鈕組件Props
 */
export interface ButtonComponentProps extends BaseComponentProps {
  title: string;                        // 按鈕文字
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'; // 按鈕變體
  size?: 'sm' | 'md' | 'lg';            // 按鈕尺寸
  isLoading?: boolean;                  // 載入狀態
  isDisabled?: boolean;                 // 禁用狀態
  icon?: string;                        // 圖標名稱
  iconPosition?: 'left' | 'right';      // 圖標位置
  onPress?: () => void;                 // 點擊回調
}

/**
 * 輸入框組件Props
 */
export interface InputComponentProps extends BaseComponentProps {
  value: string;                        // 輸入值
  placeholder?: string;                 // 佔位符
  label?: string;                       // 標籤
  error?: string;                       // 錯誤信息
  isRequired?: boolean;                 // 是否必填
  isDisabled?: boolean;                 // 是否禁用
  secureTextEntry?: boolean;            // 是否密碼輸入
  keyboardType?: 'default' | 'numeric' | 'email-address'; // 鍵盤類型
  onChangeText?: (text: string) => void; // 文字變更回調
  onFocus?: () => void;                 // 獲得焦點回調
  onBlur?: () => void;                  // 失去焦點回調
}

/**
 * 模態框組件Props
 */
export interface ModalComponentProps extends BaseComponentProps {
  isVisible: boolean;                   // 是否可見
  title?: string;                       // 標題
  showCloseButton?: boolean;            // 顯示關閉按鈕
  size?: 'sm' | 'md' | 'lg' | 'full';   // 尺寸
  position?: 'center' | 'bottom' | 'top'; // 位置
  onClose?: () => void;                 // 關閉回調
  onBackdropPress?: () => void;         // 背景點擊回調
  children: React.ReactNode;            // 子組件
}

// ==================== 頁面接口 ====================

/**
 * 主頁面Props
 */
export interface MainScreenProps {
  playerData: PlayerData;               // 玩家數據
  currentStage: number;                 // 當前關卡
  onStartBattle: (stageId: string) => void; // 開始戰鬥回調
  onNavigateToGacha: () => void;        // 導航到抽卡
  onNavigateToTeam: () => void;         // 導航到隊伍
  onNavigateToStats: () => void;        // 導航到統計
  onNavigateToSettings: () => void;     // 導航到設定
}

/**
 * 戰鬥頁面Props
 */
export interface BattleScreenProps {
  stageId: string;                      // 關卡ID
  playerTeam: Card[];                   // 玩家隊伍
  onBattleEnd: (result: BattleResult) => void; // 戰鬥結束回調
  onPause: () => void;                  // 暫停回調
  onSpeedChange: (speed: number) => void; // 速度變更回調
}

/**
 * 抽卡頁面Props
 */
export interface GachaScreenProps {
  playerData: PlayerData;               // 玩家數據
  onDrawCard: (poolId: string, count: number) => void; // 抽卡回調
  onCardObtained: (cards: Card[]) => void; // 獲得卡牌回調
}

/**
 * 隊伍管理頁面Props
 */
export interface TeamScreenProps {
  playerData: PlayerData;               // 玩家數據
  teams: Team[];                        // 隊伍列表
  currentTeamId: string;                // 當前隊伍ID
  onTeamChange: (teamId: string) => void; // 隊伍變更回調
  onTeamSave: (team: Team) => void;     // 隊伍保存回調
  onCardSelect: (card: Card, position: number) => void; // 卡牌選擇回調
}

// ==================== 動畫接口 ====================

/**
 * 動畫配置接口
 */
export interface AnimationConfig {
  duration: number;                     // 持續時間(毫秒)
  easing?: 'linear' | 'ease' | 'easeIn' | 'easeOut' | 'easeInOut'; // 緩動函數
  delay?: number;                       // 延遲時間(毫秒)
  repeat?: number;                      // 重複次數
  reverse?: boolean;                    // 是否反向
}

/**
 * 卡牌動畫類型
 */
export enum CardAnimationType {
  APPEAR = 'appear',                    // 出現
  DISAPPEAR = 'disappear',              // 消失
  ATTACK = 'attack',                    // 攻擊
  DAMAGE = 'damage',                    // 受傷
  HEAL = 'heal',                        // 治療
  BUFF = 'buff',                        // 增益
  DEBUFF = 'debuff',                    // 減益
  SELECT = 'select',                    // 選中
  HOVER = 'hover'                       // 懸停
}

/**
 * 戰鬥動畫接口
 */
export interface BattleAnimation {
  id: string;                           // 動畫ID
  type: CardAnimationType;              // 動畫類型
  targetCardId: string;                 // 目標卡牌ID
  config: AnimationConfig;              // 動畫配置
  onComplete?: () => void;              // 完成回調
}

// ==================== 響應式設計接口 ====================

/**
 * 屏幕尺寸類型
 */
export enum ScreenSize {
  SMALL = 'small',                      // <5.5吋
  MEDIUM = 'medium',                    // 5.5-6.5吋
  LARGE = 'large'                       // >6.5吋
}

/**
 * 屏幕方向類型
 */
export enum ScreenOrientation {
  PORTRAIT = 'portrait',                // 直向
  LANDSCAPE = 'landscape'               // 橫向
}

/**
 * 響應式配置接口
 */
export interface ResponsiveConfig {
  screenSize: ScreenSize;               // 屏幕尺寸
  orientation: ScreenOrientation;       // 屏幕方向
  width: number;                        // 屏幕寬度
  height: number;                       // 屏幕高度
  scale: number;                        // 縮放比例
  fontScale: number;                    // 字體縮放比例
}

/**
 * 響應式樣式接口
 */
export interface ResponsiveStyles {
  small: ViewStyle | TextStyle | ImageStyle; // 小屏樣式
  medium: ViewStyle | TextStyle | ImageStyle; // 中屏樣式
  large: ViewStyle | TextStyle | ImageStyle; // 大屏樣式
}

// ==================== 觸控交互接口 ====================

/**
 * 手勢類型
 */
export enum GestureType {
  TAP = 'tap',                          // 輕觸
  LONG_PRESS = 'longPress',             // 長按
  DOUBLE_TAP = 'doubleTap',             // 雙擊
  SWIPE_LEFT = 'swipeLeft',             // 左滑
  SWIPE_RIGHT = 'swipeRight',           // 右滑
  SWIPE_UP = 'swipeUp',                 // 上滑
  SWIPE_DOWN = 'swipeDown',             // 下滑
  PINCH = 'pinch',                      // 捏合
  PAN = 'pan'                           // 拖拽
}

/**
 * 手勢事件接口
 */
export interface GestureEvent {
  type: GestureType;                    // 手勢類型
  target: string;                       // 目標組件ID
  position: { x: number; y: number };   // 位置
  velocity?: { x: number; y: number };  // 速度
  scale?: number;                       // 縮放比例
  rotation?: number;                    // 旋轉角度
  timestamp: number;                    // 時間戳
}

/**
 * 觸覺反饋類型
 */
export enum HapticFeedbackType {
  LIGHT = 'light',                      // 輕微
  MEDIUM = 'medium',                    // 中等
  HEAVY = 'heavy',                      // 強烈
  SUCCESS = 'success',                  // 成功
  WARNING = 'warning',                  // 警告
  ERROR = 'error'                       // 錯誤
}

/**
 * 觸覺反饋接口
 */
export interface HapticFeedback {
  trigger: (type: HapticFeedbackType) => void; // 觸發觸覺反饋
  isEnabled: boolean;                   // 是否啟用
}
