<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🃏 PetingGame - Types & Interfaces 架構圖</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
        }

        .diagram-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .type-module {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-left: 5px solid;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .type-module:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .type-module.core { border-left-color: #e74c3c; }
        .type-module.config { border-left-color: #f39c12; }
        .type-module.battle { border-left-color: #e67e22; }
        .type-module.ui { border-left-color: #9b59b6; }
        .type-module.gacha { border-left-color: #3498db; }
        .type-module.utils { border-left-color: #2ecc71; }

        .module-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .module-icon {
            font-size: 2em;
            margin-right: 15px;
        }

        .module-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #2c3e50;
        }

        .module-description {
            color: #7f8c8d;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .type-list {
            list-style: none;
        }

        .type-item {
            background: #f8f9fa;
            margin: 8px 0;
            padding: 12px 15px;
            border-radius: 8px;
            border-left: 3px solid #3498db;
            transition: background 0.3s ease;
        }

        .type-item:hover {
            background: #e3f2fd;
        }

        .type-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .type-desc {
            font-size: 0.9em;
            color: #7f8c8d;
        }

        .relationships {
            margin-top: 40px;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .relationships h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.8em;
        }

        .flow-diagram {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin: 30px 0;
        }

        .flow-box {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            font-weight: bold;
            text-align: center;
            min-width: 120px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .flow-arrow {
            font-size: 1.5em;
            color: #667eea;
            font-weight: bold;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .legend {
            margin-top: 30px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }

        .legend h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .legend-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            margin-right: 10px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .diagram-container {
                grid-template-columns: 1fr;
            }
            
            .flow-diagram {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🃏 PetingGame Types & Interfaces</h1>
            <p>完整的 TypeScript 類型系統架構圖</p>
        </div>

        <div class="diagram-container">
            <!-- Core Types Module -->
            <div class="type-module core">
                <div class="module-header">
                    <div class="module-icon">🎯</div>
                    <div class="module-title">index.ts - 核心類型</div>
                </div>
                <div class="module-description">
                    定義遊戲的基礎數據結構和核心枚舉類型
                </div>
                <ul class="type-list">
                    <li class="type-item">
                        <div class="type-name">Card</div>
                        <div class="type-desc">卡牌基礎數據結構</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">BattleCard</div>
                        <div class="type-desc">戰鬥中的卡牌狀態</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">Team</div>
                        <div class="type-desc">隊伍配置</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">PlayerData</div>
                        <div class="type-desc">玩家數據</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">CardRarity</div>
                        <div class="type-desc">卡牌稀有度枚舉</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">CardRace</div>
                        <div class="type-desc">卡牌種族枚舉</div>
                    </li>
                </ul>
            </div>

            <!-- Config Types Module -->
            <div class="type-module config">
                <div class="module-header">
                    <div class="module-icon">🔧</div>
                    <div class="module-title">config.ts - 配置系統</div>
                </div>
                <div class="module-description">
                    CSV配置文件的數據結構和管理接口
                </div>
                <ul class="type-list">
                    <li class="type-item">
                        <div class="type-name">CardConfig</div>
                        <div class="type-desc">卡牌配置數據</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">SkillConfig</div>
                        <div class="type-desc">技能配置數據</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">StageConfig</div>
                        <div class="type-desc">關卡配置數據</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">GachaConfig</div>
                        <div class="type-desc">抽卡池配置</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">ConfigManager</div>
                        <div class="type-desc">配置管理器接口</div>
                    </li>
                </ul>
            </div>

            <!-- Battle Types Module -->
            <div class="type-module battle">
                <div class="module-header">
                    <div class="module-icon">⚔️</div>
                    <div class="module-title">battle.ts - 戰鬥系統</div>
                </div>
                <div class="module-description">
                    自動戰鬥系統的所有相關類型和AI接口
                </div>
                <ul class="type-list">
                    <li class="type-item">
                        <div class="type-name">Battle</div>
                        <div class="type-desc">戰鬥實例</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">BattleAction</div>
                        <div class="type-desc">戰鬥行動</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">AIDecision</div>
                        <div class="type-desc">AI決策</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">BattleManager</div>
                        <div class="type-desc">戰鬥管理器</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">DamageCalculation</div>
                        <div class="type-desc">傷害計算結果</div>
                    </li>
                </ul>
            </div>

            <!-- UI Types Module -->
            <div class="type-module ui">
                <div class="module-header">
                    <div class="module-icon">🎨</div>
                    <div class="module-title">ui.ts - UI系統</div>
                </div>
                <div class="module-description">
                    UI組件、主題系統和響應式設計的類型定義
                </div>
                <ul class="type-list">
                    <li class="type-item">
                        <div class="type-name">Theme</div>
                        <div class="type-desc">主題配置</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">CardComponentProps</div>
                        <div class="type-desc">卡牌組件Props</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">ButtonComponentProps</div>
                        <div class="type-desc">按鈕組件Props</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">ResponsiveConfig</div>
                        <div class="type-desc">響應式配置</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">AnimationConfig</div>
                        <div class="type-desc">動畫配置</div>
                    </li>
                </ul>
            </div>

            <!-- Gacha Types Module -->
            <div class="type-module gacha">
                <div class="module-header">
                    <div class="module-icon">🎴</div>
                    <div class="module-title">gacha.ts - 抽卡系統</div>
                </div>
                <div class="module-description">
                    抽卡系統的請求、結果和動畫類型定義
                </div>
                <ul class="type-list">
                    <li class="type-item">
                        <div class="type-name">GachaRequest</div>
                        <div class="type-desc">抽卡請求</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">GachaResult</div>
                        <div class="type-desc">抽卡結果</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">GachaAnimation</div>
                        <div class="type-desc">抽卡動畫</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">GachaManager</div>
                        <div class="type-desc">抽卡管理器</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">GachaHistory</div>
                        <div class="type-desc">抽卡歷史</div>
                    </li>
                </ul>
            </div>

            <!-- Utils Module -->
            <div class="type-module utils">
                <div class="module-header">
                    <div class="module-icon">🛠️</div>
                    <div class="module-title">utils.ts - 工具函數</div>
                </div>
                <div class="module-description">
                    類型守衛、驗證函數和工具方法
                </div>
                <ul class="type-list">
                    <li class="type-item">
                        <div class="type-name">isCard()</div>
                        <div class="type-desc">卡牌類型守衛</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">parseRarity()</div>
                        <div class="type-desc">稀有度解析</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">isValidId()</div>
                        <div class="type-desc">ID格式驗證</div>
                    </li>
                    <li class="type-item">
                        <div class="type-name">hasRequiredProperties()</div>
                        <div class="type-desc">屬性結構檢查</div>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Type Relationships -->
        <div class="relationships">
            <h2>🔗 類型關係流程圖</h2>
            <div class="flow-diagram">
                <div class="flow-box">Core Types</div>
                <div class="flow-arrow">→</div>
                <div class="flow-box">Config System</div>
                <div class="flow-arrow">→</div>
                <div class="flow-box">Battle System</div>
                <div class="flow-arrow">→</div>
                <div class="flow-box">UI Components</div>
            </div>
            <div class="flow-diagram">
                <div class="flow-box">Utils</div>
                <div class="flow-arrow">↕️</div>
                <div class="flow-box">All Modules</div>
                <div class="flow-arrow">↕️</div>
                <div class="flow-box">Constants</div>
            </div>

            <!-- Detailed Type Hierarchy -->
            <div style="margin-top: 40px;">
                <h3 style="text-align: center; margin-bottom: 20px; color: #2c3e50;">📊 詳細類型層次結構</h3>
                <div class="mermaid-container" style="background: #f8f9fa; padding: 20px; border-radius: 10px; overflow-x: auto;">
                    <div class="mermaid">
                        graph TD
                            A[index.ts<br/>核心類型] --> B[Card]
                            A --> C[BattleCard]
                            A --> D[Team]
                            A --> E[PlayerData]
                            A --> F[CardRarity]
                            A --> G[CardRace]

                            H[config.ts<br/>配置系統] --> I[CardConfig]
                            H --> J[SkillConfig]
                            H --> K[StageConfig]
                            H --> L[ConfigManager]

                            M[battle.ts<br/>戰鬥系統] --> N[Battle]
                            M --> O[BattleAction]
                            M --> P[AIDecision]
                            M --> Q[BattleManager]

                            R[ui.ts<br/>UI系統] --> S[Theme]
                            R --> T[CardComponentProps]
                            R --> U[ButtonComponentProps]

                            V[gacha.ts<br/>抽卡系統] --> W[GachaRequest]
                            V --> X[GachaResult]
                            V --> Y[GachaManager]

                            Z[utils.ts<br/>工具函數] --> AA[isCard]
                            Z --> BB[parseRarity]
                            Z --> CC[Type Guards]

                            B --> C
                            B --> I
                            C --> N
                            C --> O
                            F --> I
                            G --> I

                            style A fill:#e74c3c,stroke:#c0392b,color:#fff
                            style H fill:#f39c12,stroke:#e67e22,color:#fff
                            style M fill:#e67e22,stroke:#d35400,color:#fff
                            style R fill:#9b59b6,stroke:#8e44ad,color:#fff
                            style V fill:#3498db,stroke:#2980b9,color:#fff
                            style Z fill:#2ecc71,stroke:#27ae60,color:#fff
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">6</div>
                <div class="stat-label">類型模塊</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">50+</div>
                <div class="stat-label">接口定義</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">15+</div>
                <div class="stat-label">枚舉類型</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">30+</div>
                <div class="stat-label">工具函數</div>
            </div>
        </div>

        <!-- Legend -->
        <div class="legend">
            <h3>🎨 模塊顏色說明</h3>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #e74c3c;"></div>
                    <span>核心類型 - 基礎數據結構</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #f39c12;"></div>
                    <span>配置系統 - CSV配置管理</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #e67e22;"></div>
                    <span>戰鬥系統 - 自動戰鬥邏輯</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #9b59b6;"></div>
                    <span>UI系統 - 界面組件</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #3498db;"></div>
                    <span>抽卡系統 - 卡牌召喚</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #2ecc71;"></div>
                    <span>工具函數 - 類型安全</span>
                </div>
            </div>
        </div>

        <!-- Interactive Features -->
        <div style="margin-top: 30px; text-align: center;">
            <button onclick="toggleDetails()" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 1.1em; cursor: pointer; margin: 0 10px;">
                🔍 切換詳細信息
            </button>
            <button onclick="exportDiagram()" style="background: linear-gradient(135deg, #2ecc71, #27ae60); color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 1.1em; cursor: pointer; margin: 0 10px;">
                📥 導出圖表
            </button>
            <button onclick="printPage()" style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 1.1em; cursor: pointer; margin: 0 10px;">
                🖨️ 打印頁面
            </button>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // 切換詳細信息顯示
        function toggleDetails() {
            const typeDescs = document.querySelectorAll('.type-desc');
            typeDescs.forEach(desc => {
                desc.style.display = desc.style.display === 'none' ? 'block' : 'none';
            });
        }

        // 導出圖表功能
        function exportDiagram() {
            const content = document.querySelector('.container').innerHTML;
            const blob = new Blob([`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>PetingGame Types Diagram</title>
                    <style>${document.querySelector('style').innerHTML}</style>
                </head>
                <body>
                    <div class="container">${content}</div>
                </body>
                </html>
            `], { type: 'text/html' });

            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'petinggame-types-diagram.html';
            a.click();
            URL.revokeObjectURL(url);
        }

        // 打印頁面
        function printPage() {
            window.print();
        }

        // 添加懸停效果
        document.addEventListener('DOMContentLoaded', function() {
            const typeItems = document.querySelectorAll('.type-item');
            typeItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(5px)';
                    this.style.borderLeftWidth = '5px';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                    this.style.borderLeftWidth = '3px';
                });
            });

            // 添加模塊點擊展開/收縮功能
            const moduleHeaders = document.querySelectorAll('.module-header');
            moduleHeaders.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', function() {
                    const module = this.parentElement;
                    const typeList = module.querySelector('.type-list');
                    const isCollapsed = typeList.style.display === 'none';

                    typeList.style.display = isCollapsed ? 'block' : 'none';

                    // 添加展開/收縮圖標
                    const icon = this.querySelector('.module-icon');
                    icon.style.transform = isCollapsed ? 'rotate(0deg)' : 'rotate(-90deg)';
                });
            });

            // 添加搜索功能
            const searchInput = document.createElement('input');
            searchInput.type = 'text';
            searchInput.placeholder = '🔍 搜索類型...';
            searchInput.style.cssText = `
                width: 100%;
                max-width: 400px;
                padding: 12px 20px;
                margin: 20px auto;
                display: block;
                border: 2px solid #667eea;
                border-radius: 25px;
                font-size: 1.1em;
                outline: none;
                transition: all 0.3s ease;
            `;

            searchInput.addEventListener('focus', function() {
                this.style.boxShadow = '0 0 20px rgba(102, 126, 234, 0.3)';
            });

            searchInput.addEventListener('blur', function() {
                this.style.boxShadow = 'none';
            });

            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const typeItems = document.querySelectorAll('.type-item');

                typeItems.forEach(item => {
                    const typeName = item.querySelector('.type-name').textContent.toLowerCase();
                    const typeDesc = item.querySelector('.type-desc').textContent.toLowerCase();

                    if (typeName.includes(searchTerm) || typeDesc.includes(searchTerm)) {
                        item.style.display = 'block';
                        item.style.background = searchTerm ? '#e3f2fd' : '#f8f9fa';
                    } else {
                        item.style.display = searchTerm ? 'none' : 'block';
                        item.style.background = '#f8f9fa';
                    }
                });
            });

            document.querySelector('.header').appendChild(searchInput);
        });
    </script>
</body>
</html>
