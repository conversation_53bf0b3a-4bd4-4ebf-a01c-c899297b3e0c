<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Main Menu Layout - PetingGame</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Times New Roman', serif;
            background: linear-gradient(135deg, #f7f3e9 0%, #e8dcc0 50%, #d4c5a0 100%);
            min-height: 100vh;
            color: #3c2415;
            overflow-x: hidden;
        }

        /* 手機直向佈局 */
        .mobile-container {
            max-width: 375px;
            margin: 0 auto;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 狀態欄 */
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 20px;
            background: rgba(139, 69, 19, 0.2);
            backdrop-filter: blur(10px);
            font-size: 14px;
            font-weight: bold;
            font-family: 'Times New Roman', serif;
            color: #8b4513;
            border-bottom: 2px dashed rgba(139, 69, 19, 0.3);
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .resource {
            display: flex;
            align-items: center;
            gap: 4px;
            background: #f4a261;
            color: #8b4513;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            border: 2px solid #8b4513;
            box-shadow: 2px 2px 0px rgba(139, 69, 19, 0.3);
            transform: rotate(-1deg);
            font-family: 'Times New Roman', serif;
        }

        /* 主要內容區域 */
        .main-content {
            flex: 1;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* 關卡標題區域 */
        .stage-header {
            text-align: center;
            margin-bottom: 10px;
        }

        .stage-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .stage-progress {
            font-size: 16px;
            opacity: 0.9;
        }

        /* 戰鬥預覽區域 */
        .battle-preview {
            background: rgba(255,255,255,0.1);
            border-radius: 16px;
            padding: 30px 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .preview-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .enemy-preview {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .mini-enemy {
            width: 40px;
            height: 40px;
            background: rgba(239,68,68,0.8);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .start-battle-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            padding: 16px 40px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(16,185,129,0.3);
        }

        .start-battle-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16,185,129,0.4);
        }

        .start-battle-btn:active {
            transform: translateY(0);
        }

        /* 隊伍狀態區域 */
        .team-status {
            background: rgba(255,255,255,0.05);
            border-radius: 12px;
            padding: 15px;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .team-power {
            font-size: 16px;
            margin-bottom: 12px;
            font-weight: bold;
        }

        .team-preview {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .team-card {
            width: 35px;
            height: 45px;
            background: linear-gradient(145deg, #3b82f6, #1d4ed8);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            border: 1px solid rgba(255,255,255,0.3);
            position: relative;
        }

        .team-card.dragon { background: linear-gradient(145deg, #ef4444, #dc2626); }
        .team-card.elf { background: linear-gradient(145deg, #10b981, #059669); }
        .team-card.orc { background: linear-gradient(145deg, #f59e0b, #d97706); }

        .team-count {
            margin-left: auto;
            font-size: 14px;
            opacity: 0.8;
        }

        /* FAB 快速操作按鈕 */
        .fab {
            position: absolute;
            bottom: 80px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(139,92,246,0.3);
            z-index: 10;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 30px rgba(139,92,246,0.4);
        }

        /* 底部導航 */
        .bottom-nav {
            background: rgba(0,0,0,0.8);
            backdrop-filter: blur(20px);
            padding: 12px 0;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .nav-tabs {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-tab {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            border-radius: 12px;
            transition: all 0.2s ease;
            cursor: pointer;
            min-width: 48px;
            min-height: 48px;
            justify-content: center;
        }

        .nav-tab:hover {
            background: rgba(255,255,255,0.1);
        }

        .nav-tab.active {
            background: rgba(59,130,246,0.3);
            color: #60a5fa;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        /* 響應式調整 */
        @media (max-width: 360px) {
            .mobile-container {
                max-width: 100%;
            }
            
            .main-content {
                padding: 15px;
            }
            
            .stage-title {
                font-size: 20px;
            }
            
            .start-battle-btn {
                padding: 14px 30px;
                font-size: 16px;
            }
        }

        /* 動畫效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .main-content > * {
            animation: fadeIn 0.6s ease forwards;
        }

        .main-content > *:nth-child(2) { animation-delay: 0.1s; }
        .main-content > *:nth-child(3) { animation-delay: 0.2s; }
        .main-content > *:nth-child(4) { animation-delay: 0.3s; }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- 狀態欄 -->
        <div class="status-bar">
            <div class="status-left">
                <span>🔋 95%</span>
                <span>15:30</span>
            </div>
            <div class="status-right">
                <div class="resource">
                    <span>💎</span>
                    <span>1,250</span>
                </div>
                <div class="resource">
                    <span>💰</span>
                    <span>5,600</span>
                </div>
            </div>
        </div>

        <!-- 主要內容 -->
        <div class="main-content">
            <!-- 關卡標題 -->
            <div class="stage-header">
                <h1 class="stage-title">🏆 第5關 森林深處</h1>
                <div class="stage-progress">進度: 3/3 ⭐⭐⭐</div>
            </div>

            <!-- 戰鬥預覽區域 -->
            <div class="battle-preview">
                <div class="preview-content">
                    <h3 style="margin-bottom: 15px; opacity: 0.9;">即將面對的敵人</h3>
                    <div class="enemy-preview">
                        <div class="mini-enemy">🐺</div>
                        <div class="mini-enemy">🧌</div>
                        <div class="mini-enemy">🕷️</div>
                    </div>
                    <div style="opacity: 0.8; margin-bottom: 20px;">
                        敵方戰力: 980
                    </div>
                    <button class="start-battle-btn">
                        ▶️ 開始戰鬥
                    </button>
                </div>
            </div>

            <!-- 隊伍狀態 -->
            <div class="team-status">
                <div class="team-power">📊 隊伍戰力: 1,250</div>
                <div class="team-preview">
                    <span style="opacity: 0.9; margin-right: 8px;">👥</span>
                    <div class="team-card dragon">🔥</div>
                    <div class="team-card elf">🏹</div>
                    <div class="team-card orc">🪓</div>
                    <div class="team-count">3/6</div>
                </div>
            </div>
        </div>

        <!-- FAB 快速操作按鈕 -->
        <div class="fab" title="快速操作">
            🚀
        </div>

        <!-- 底部導航 -->
        <div class="bottom-nav">
            <div class="nav-tabs">
                <div class="nav-tab active">
                    <div class="nav-icon">⚔️</div>
                    <div class="nav-label">戰鬥</div>
                </div>
                <div class="nav-tab">
                    <div class="nav-icon">🎴</div>
                    <div class="nav-label">抽卡</div>
                </div>
                <div class="nav-tab">
                    <div class="nav-icon">👥</div>
                    <div class="nav-label">隊伍</div>
                </div>
                <div class="nav-tab">
                    <div class="nav-icon">📊</div>
                    <div class="nav-label">數據</div>
                </div>
                <div class="nav-tab">
                    <div class="nav-icon">⚙️</div>
                    <div class="nav-label">設定</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 底部導航交互
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 開始戰鬥按鈕效果
        document.querySelector('.start-battle-btn').addEventListener('click', function() {
            this.style.background = 'linear-gradient(135deg, #059669 0%, #047857 100%)';
            this.textContent = '⚔️ 戰鬥中...';
            setTimeout(() => {
                this.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
                this.textContent = '▶️ 開始戰鬥';
            }, 2000);
        });

        // FAB 按鈕效果
        document.querySelector('.fab').addEventListener('click', function() {
            this.style.transform = 'scale(1.2) rotate(180deg)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 300);
        });
    </script>
</body>
</html>