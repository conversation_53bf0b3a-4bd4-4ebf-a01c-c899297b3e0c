<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>戰鬥界面 - 3種設計風格</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #1a1a1a;
            color: white;
            overflow-x: hidden;
        }

        .design-container {
            max-width: 375px;
            margin: 0 auto;
            min-height: 100vh;
            position: relative;
            border: 2px solid #333;
            margin-bottom: 40px;
            display: flex;
            flex-direction: column;
        }

        .design-title {
            position: absolute;
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: #000;
            padding: 5px 15px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 1000;
        }

        /* ========== 競技電競風格 ========== */
        .esports {
            background: linear-gradient(135deg, #ff6b35 0%, #004e89 50%, #ff1744 100%);
        }

        .esports .battle-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            background: rgba(0, 0, 0, 0.8);
            border-bottom: 3px solid #ff6b35;
            box-shadow: 0 0 20px #ff6b35;
        }

        .esports .stage-info {
            font-size: 16px;
            font-weight: bold;
            color: #ff6b35;
            text-transform: uppercase;
            font-family: 'Arial Black', Arial, sans-serif;
        }

        .esports .header-controls button {
            width: 36px;
            height: 36px;
            background: linear-gradient(45deg, #ff6b35, #ff1744);
            border: 2px solid #fff;
            border-radius: 4px;
            color: white;
            font-size: 14px;
            margin-left: 8px;
            box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
            transition: all 0.3s ease;
        }

        .esports .header-controls button:hover {
            transform: scale(1.1);
            box-shadow: 0 0 20px rgba(255, 23, 68, 0.8);
        }

        .esports .enemy-section {
            background: linear-gradient(135deg, rgba(255, 23, 68, 0.2), rgba(255, 107, 53, 0.1));
            padding: 15px;
            border-bottom: 3px solid #ff1744;
            min-height: 30vh;
            position: relative;
        }

        .esports .enemy-section:before {
            content: 'ENEMY TEAM';
            position: absolute;
            top: 5px;
            left: 20px;
            font-size: 12px;
            font-weight: bold;
            color: #ff1744;
            text-transform: uppercase;
        }

        .esports .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 14px;
            font-weight: bold;
            color: #ff1744;
            text-transform: uppercase;
        }

        .esports .battle-card {
            width: 80px;
            height: 100px;
            background: linear-gradient(145deg, rgba(0, 0, 0, 0.8), rgba(255, 23, 68, 0.2));
            border: 2px solid #ff1744;
            border-radius: 4px;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            padding: 8px 4px;
            font-size: 12px;
            box-shadow: 0 0 15px rgba(255, 23, 68, 0.3);
            transition: all 0.3s ease;
        }

        .esports .battle-card:hover {
            transform: scale(1.05);
            box-shadow: 0 0 25px rgba(255, 107, 53, 0.6);
        }

        .esports .action-section {
            background: rgba(0, 0, 0, 0.9);
            padding: 15px 20px;
            border-top: 2px solid #ff6b35;
            border-bottom: 2px solid #ff6b35;
            text-align: center;
        }

        .esports .next-action {
            font-size: 14px;
            color: #ff6b35;
            font-weight: bold;
            text-transform: uppercase;
            margin-bottom: 10px;
        }

        .esports .action-indicator {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            border: 2px solid #fff;
            text-transform: uppercase;
        }

        .esports .action-indicator.player {
            background: linear-gradient(135deg, #004e89, #ff6b35);
        }

        .esports .action-indicator.enemy {
            background: linear-gradient(135deg, #ff1744, #ff6b35);
        }

        .esports .player-section {
            background: linear-gradient(135deg, rgba(0, 78, 137, 0.2), rgba(255, 107, 53, 0.1));
            padding: 15px;
            border-bottom: 3px solid #004e89;
            min-height: 40vh;
            flex: 1;
            position: relative;
        }

        .esports .player-section:before {
            content: 'YOUR TEAM';
            position: absolute;
            top: 5px;
            left: 20px;
            font-size: 12px;
            font-weight: bold;
            color: #004e89;
            text-transform: uppercase;
        }

        .esports .player-header {
            color: #004e89;
        }

        .esports .player-card {
            width: 90px;
            height: 120px;
            background: linear-gradient(145deg, rgba(0, 78, 137, 0.8), rgba(255, 107, 53, 0.2));
            border: 2px solid #004e89;
            border-radius: 4px;
            padding: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: 0 0 15px rgba(0, 78, 137, 0.3);
        }

        .esports .player-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
        }

        .esports .player-card.ready {
            border-color: #ff6b35;
            animation: esportsPulse 1.5s infinite;
            box-shadow: 0 0 30px rgba(255, 107, 53, 0.6);
        }

        .esports .battle-controls {
            background: rgba(0, 0, 0, 0.9);
            padding: 15px 20px;
            display: flex;
            justify-content: space-around;
            gap: 10px;
            border-top: 3px solid #ff6b35;
        }

        .esports .battle-btn {
            flex: 1;
            padding: 12px 8px;
            background: linear-gradient(45deg, #ff6b35, #ff1744);
            border: 2px solid #fff;
            border-radius: 4px;
            color: white;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            transition: all 0.3s ease;
            box-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
        }

        .esports .battle-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(255, 23, 68, 0.5);
        }

        /* ========== 動漫卡通風格 ========== */
        .anime {
            background: linear-gradient(135deg, #ffb3d9 0%, #87ceeb 50%, #ffd700 100%);
        }

        .anime .battle-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            background: linear-gradient(90deg, #ffb3d9, #87ceeb);
            border-bottom: 3px solid #ffd700;
            border-radius: 0 0 20px 20px;
        }

        .anime .stage-info {
            font-size: 16px;
            font-weight: bold;
            color: #ff1493;
            font-family: 'Comic Sans MS', cursive;
        }

        .anime .header-controls button {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #ffd700, #ffb3d9);
            border: 3px solid #ff1493;
            border-radius: 50%;
            color: #ff1493;
            font-size: 16px;
            margin-left: 8px;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.5);
            transition: all 0.3s ease;
        }

        .anime .header-controls button:hover {
            transform: rotate(15deg) scale(1.1);
            box-shadow: 0 8px 25px rgba(255, 20, 147, 0.6);
        }

        .anime .enemy-section {
            background: linear-gradient(135deg, rgba(255, 20, 147, 0.2), rgba(255, 179, 217, 0.3));
            padding: 20px;
            border-bottom: 3px solid #ff1493;
            border-radius: 0 0 30px 30px;
            min-height: 30vh;
            position: relative;
        }

        .anime .enemy-section:before {
            content: '💢 壞蛋們 💢';
            position: absolute;
            top: 5px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 14px;
            font-weight: bold;
            color: #ff1493;
            background: #ffd700;
            padding: 5px 15px;
            border-radius: 20px;
            border: 2px solid #ff1493;
        }

        .anime .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 14px;
            font-weight: bold;
            color: #ff1493;
            font-family: 'Comic Sans MS', cursive;
        }

        .anime .battle-card {
            width: 80px;
            height: 100px;
            background: linear-gradient(145deg, #ffb3d9, #ffd700);
            border: 3px solid #ff1493;
            border-radius: 20px;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            padding: 8px 4px;
            font-size: 12px;
            box-shadow: 0 8px 20px rgba(255, 20, 147, 0.3);
            transition: all 0.3s ease;
        }

        .anime .battle-card:hover {
            transform: scale(1.1) rotate(-5deg);
            box-shadow: 0 12px 30px rgba(255, 215, 0, 0.5);
        }

        .anime .action-section {
            background: linear-gradient(90deg, #87ceeb, #ffb3d9);
            padding: 20px;
            text-align: center;
            border-radius: 30px;
            margin: 10px 15px;
        }

        .anime .next-action {
            font-size: 16px;
            color: #ff1493;
            font-weight: bold;
            font-family: 'Comic Sans MS', cursive;
            margin-bottom: 15px;
        }

        .anime .action-indicator {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            border: 3px solid #fff;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            animation: bounce 2s infinite;
        }

        .anime .action-indicator.player {
            background: linear-gradient(135deg, #87ceeb, #ffd700);
            color: #ff1493;
        }

        .anime .action-indicator.enemy {
            background: linear-gradient(135deg, #ffb3d9, #ff1493);
            color: white;
        }

        .anime .player-section {
            background: linear-gradient(135deg, rgba(135, 206, 235, 0.3), rgba(255, 215, 0, 0.2));
            padding: 20px;
            border-bottom: 3px solid #87ceeb;
            border-radius: 30px 30px 0 0;
            min-height: 40vh;
            flex: 1;
            position: relative;
        }

        .anime .player-section:before {
            content: '🌟 我的隊伍 🌟';
            position: absolute;
            top: 5px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 14px;
            font-weight: bold;
            color: #87ceeb;
            background: #ffd700;
            padding: 5px 15px;
            border-radius: 20px;
            border: 2px solid #87ceeb;
        }

        .anime .player-card {
            width: 90px;
            height: 120px;
            background: linear-gradient(145deg, #ffd700, #87ceeb);
            border: 3px solid #87ceeb;
            border-radius: 20px;
            padding: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: 0 8px 20px rgba(135, 206, 235, 0.3);
        }

        .anime .player-card:hover {
            transform: translateY(-8px) rotate(5deg);
            box-shadow: 0 15px 35px rgba(255, 215, 0, 0.5);
        }

        .anime .player-card.ready {
            border-color: #ff1493;
            animation: rainbowPulse 2s infinite;
            box-shadow: 0 0 30px rgba(255, 20, 147, 0.6);
        }

        .anime .battle-controls {
            background: linear-gradient(90deg, #ffb3d9, #87ceeb, #ffd700);
            padding: 20px;
            display: flex;
            justify-content: space-around;
            gap: 15px;
            border-radius: 30px 30px 0 0;
        }

        .anime .battle-btn {
            flex: 1;
            padding: 15px 10px;
            background: linear-gradient(45deg, #ffd700, #ffb3d9);
            border: 3px solid #ff1493;
            border-radius: 20px;
            color: #ff1493;
            font-size: 12px;
            font-weight: bold;
            font-family: 'Comic Sans MS', cursive;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 20, 147, 0.3);
        }

        .anime .battle-btn:hover {
            transform: translateY(-4px) scale(1.05);
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.5);
        }

        /* ========== 暗黑哥特風格 ========== */
        .gothic {
            background: linear-gradient(135deg, #0f0f0f 0%, #8b0000 50%, #2d1b69 100%);
        }

        .gothic .battle-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            background: rgba(0, 0, 0, 0.9);
            border-bottom: 2px solid #8b0000;
            box-shadow: 0 4px 20px rgba(139, 0, 0, 0.5);
        }

        .gothic .stage-info {
            font-size: 16px;
            font-weight: bold;
            color: #dc143c;
            font-family: serif;
            text-shadow: 0 0 10px #8b0000;
        }

        .gothic .header-controls button {
            width: 36px;
            height: 36px;
            background: linear-gradient(45deg, #8b0000, #2d1b69);
            border: 2px solid #dc143c;
            border-radius: 0;
            color: #f8f8ff;
            font-size: 14px;
            margin-left: 8px;
            box-shadow: 0 0 15px rgba(139, 0, 0, 0.5);
            transition: all 0.3s ease;
            position: relative;
        }

        .gothic .header-controls button:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, #dc143c, transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .gothic .header-controls button:hover:before {
            opacity: 0.3;
        }

        .gothic .enemy-section {
            background: linear-gradient(135deg, rgba(139, 0, 0, 0.3), rgba(15, 15, 15, 0.8));
            padding: 15px;
            border-bottom: 3px solid #8b0000;
            min-height: 30vh;
            position: relative;
        }

        .gothic .enemy-section:before {
            content: '💀 SERVANTS OF DARKNESS 💀';
            position: absolute;
            top: 5px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            font-weight: bold;
            color: #dc143c;
            font-family: serif;
            text-shadow: 0 0 10px #8b0000;
        }

        .gothic .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 14px;
            font-weight: bold;
            color: #dc143c;
            font-family: serif;
            text-shadow: 0 0 5px #8b0000;
        }

        .gothic .battle-card {
            width: 80px;
            height: 100px;
            background: linear-gradient(145deg, rgba(15, 15, 15, 0.9), rgba(139, 0, 0, 0.3));
            border: 2px solid #8b0000;
            border-radius: 0;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            padding: 8px 4px;
            font-size: 12px;
            box-shadow: 0 8px 20px rgba(139, 0, 0, 0.4);
            transition: all 0.3s ease;
        }

        .gothic .battle-card:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 30px rgba(220, 20, 60, 0.6);
            border-color: #dc143c;
        }

        .gothic .battle-card:before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #8b0000, #2d1b69, #8b0000);
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .gothic .battle-card:hover:before {
            opacity: 1;
        }

        .gothic .action-section {
            background: rgba(15, 15, 15, 0.9);
            padding: 15px 20px;
            border-top: 1px solid #8b0000;
            border-bottom: 1px solid #8b0000;
            text-align: center;
        }

        .gothic .next-action {
            font-size: 14px;
            color: #dc143c;
            font-weight: bold;
            font-family: serif;
            margin-bottom: 10px;
            text-shadow: 0 0 10px #8b0000;
        }

        .gothic .action-indicator {
            width: 32px;
            height: 32px;
            border-radius: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            border: 2px solid #8b0000;
            font-family: serif;
            position: relative;
        }

        .gothic .action-indicator.player {
            background: linear-gradient(135deg, #2d1b69, #8b0000);
            color: #f8f8ff;
        }

        .gothic .action-indicator.enemy {
            background: linear-gradient(135deg, #8b0000, #dc143c);
            color: #f8f8ff;
        }

        .gothic .player-section {
            background: linear-gradient(135deg, rgba(45, 27, 105, 0.3), rgba(15, 15, 15, 0.8));
            padding: 15px;
            border-bottom: 3px solid #2d1b69;
            min-height: 40vh;
            flex: 1;
            position: relative;
        }

        .gothic .player-section:before {
            content: '⚔️ LEGION OF THE DAMNED ⚔️';
            position: absolute;
            top: 5px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            font-weight: bold;
            color: #2d1b69;
            font-family: serif;
            text-shadow: 0 0 10px #2d1b69;
        }

        .gothic .player-card {
            width: 90px;
            height: 120px;
            background: linear-gradient(145deg, rgba(45, 27, 105, 0.8), rgba(139, 0, 0, 0.3));
            border: 2px solid #2d1b69;
            border-radius: 0;
            padding: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: 0 8px 20px rgba(45, 27, 105, 0.4);
        }

        .gothic .player-card:hover {
            transform: translateY(-6px);
            box-shadow: 0 15px 35px rgba(139, 0, 0, 0.5);
            border-color: #8b0000;
        }

        .gothic .player-card.ready {
            border-color: #dc143c;
            animation: bloodPulse 2s infinite;
            box-shadow: 0 0 30px rgba(220, 20, 60, 0.6);
        }

        .gothic .battle-controls {
            background: rgba(15, 15, 15, 0.9);
            padding: 15px 20px;
            display: flex;
            justify-content: space-around;
            gap: 10px;
            border-top: 3px solid #8b0000;
        }

        .gothic .battle-btn {
            flex: 1;
            padding: 12px 8px;
            background: linear-gradient(45deg, #8b0000, #2d1b69);
            border: 2px solid #dc143c;
            border-radius: 0;
            color: #f8f8ff;
            font-size: 12px;
            font-weight: bold;
            font-family: serif;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(139, 0, 0, 0.3);
            text-shadow: 0 0 5px #8b0000;
        }

        .gothic .battle-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 20, 60, 0.5);
            text-shadow: 0 0 10px #dc143c;
        }

        /* 動畫效果 */
        @keyframes esportsPulse {
            0%, 100% { box-shadow: 0 0 30px rgba(255, 107, 53, 0.6); }
            50% { box-shadow: 0 0 50px rgba(255, 23, 68, 0.8), 0 0 70px rgba(255, 107, 53, 0.4); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        @keyframes rainbowPulse {
            0% { box-shadow: 0 0 30px rgba(255, 20, 147, 0.6); }
            33% { box-shadow: 0 0 30px rgba(135, 206, 235, 0.6); }
            66% { box-shadow: 0 0 30px rgba(255, 215, 0, 0.6); }
            100% { box-shadow: 0 0 30px rgba(255, 20, 147, 0.6); }
        }

        @keyframes bloodPulse {
            0%, 100% { box-shadow: 0 0 30px rgba(220, 20, 60, 0.6); }
            50% { box-shadow: 0 0 50px rgba(139, 0, 0, 0.8), 0 0 70px rgba(220, 20, 60, 0.4); }
        }

        /* 響應式設計 */
        @media (max-width: 360px) {
            .design-container {
                max-width: 100%;
            }
            
            .battle-card, .player-card {
                width: 70px;
                height: 90px;
            }
        }
    </style>
</head>
<body>
    <!-- 競技電競風格 -->
    <div class="design-container esports">
        <div class="design-title">🎮 競技電競風格</div>
        
        <div class="battle-header">
            <div class="stage-info">⚔️ MATCH 1-5</div>
            <div class="header-controls">
                <button title="暫停">⏸️</button>
                <button title="音效">🔊</button>
                <button title="設定">⚙️</button>
            </div>
        </div>

        <div class="enemy-section">
            <div class="section-header">
                <span>ENEMY SQUAD (3/3)</span>
                <span>HP: 450</span>
            </div>
            <div style="display: flex; justify-content: center; gap: 12px; margin-top: 20px;">
                <div class="battle-card">
                    <div style="font-size: 20px; margin-bottom: 4px;">🐺</div>
                    <div style="display: flex; gap: 4px; font-size: 10px; font-weight: bold;">
                        <span style="color: #ff1744;">⚔️65</span>
                        <span style="color: #ff6b35;">⚡12</span>
                    </div>
                    <div style="width: 100%; height: 4px; background: rgba(0,0,0,0.3); border-radius: 2px; margin-top: 4px;">
                        <div style="width: 80%; height: 100%; background: linear-gradient(90deg, #ff1744, #ff6b35); border-radius: 2px;"></div>
                    </div>
                </div>
                <div class="battle-card">
                    <div style="font-size: 20px; margin-bottom: 4px;">🧌</div>
                    <div style="display: flex; gap: 4px; font-size: 10px; font-weight: bold;">
                        <span style="color: #ff1744;">⚔️85</span>
                        <span style="color: #ff6b35;">⚡8</span>
                    </div>
                    <div style="width: 100%; height: 4px; background: rgba(0,0,0,0.3); border-radius: 2px; margin-top: 4px;">
                        <div style="width: 60%; height: 100%; background: linear-gradient(90deg, #ff1744, #ff6b35); border-radius: 2px;"></div>
                    </div>
                </div>
                <div class="battle-card">
                    <div style="font-size: 20px; margin-bottom: 4px;">🕷️</div>
                    <div style="display: flex; gap: 4px; font-size: 10px; font-weight: bold;">
                        <span style="color: #ff1744;">⚔️45</span>
                        <span style="color: #ff6b35;">⚡18</span>
                    </div>
                    <div style="width: 100%; height: 4px; background: rgba(0,0,0,0.3); border-radius: 2px; margin-top: 4px;">
                        <div style="width: 90%; height: 100%; background: linear-gradient(90deg, #ff1744, #ff6b35); border-radius: 2px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="action-section">
            <div class="next-action">⚡ NEXT: FIRE WARRIOR (2s)</div>
            <div style="display: flex; justify-content: center; gap: 8px; align-items: center;">
                <div class="action-indicator player">YOU</div>
                <div class="action-indicator enemy">FOE</div>
                <div class="action-indicator player">YOU</div>
                <div class="action-indicator enemy">FOE</div>
                <div class="action-indicator player">YOU</div>
            </div>
        </div>

        <div class="player-section">
            <div class="section-header player-header">
                <span>YOUR SQUAD (3/3)</span>
                <span>HP: 850</span>
            </div>
            <div style="display: flex; justify-content: center; gap: 12px; margin-top: 20px;">
                <div class="player-card ready">
                    <div style="position: absolute; top: -4px; left: -4px; background: #ff6b35; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 10px; font-weight: bold;">5</div>
                    <div style="font-size: 20px; margin-bottom: 4px;">🔥</div>
                    <div style="font-size: 11px; font-weight: bold; text-align: center; margin: 4px 0;">FIRE WARRIOR</div>
                    <div style="display: flex; justify-content: space-between; width: 100%; font-size: 10px; font-weight: bold;">
                        <span style="color: #ff1744;">⚔️120</span>
                        <span style="color: #ff6b35;">⚡15</span>
                    </div>
                    <div style="width: 100%; height: 4px; background: rgba(0,0,0,0.3); border-radius: 2px; margin-top: 4px;">
                        <div style="width: 100%; height: 100%; background: linear-gradient(90deg, #004e89, #ff6b35); border-radius: 2px;"></div>
                    </div>
                </div>
                <div class="player-card">
                    <div style="position: absolute; top: -4px; left: -4px; background: #ff6b35; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 10px; font-weight: bold;">3</div>
                    <div style="font-size: 20px; margin-bottom: 4px;">🏹</div>
                    <div style="font-size: 11px; font-weight: bold; text-align: center; margin: 4px 0;">ARCHER</div>
                    <div style="display: flex; justify-content: space-between; width: 100%; font-size: 10px; font-weight: bold;">
                        <span style="color: #ff1744;">⚔️85</span>
                        <span style="color: #ff6b35;">⚡18</span>
                    </div>
                    <div style="width: 100%; height: 4px; background: rgba(0,0,0,0.3); border-radius: 2px; margin-top: 4px;">
                        <div style="width: 75%; height: 100%; background: linear-gradient(90deg, #004e89, #ff6b35); border-radius: 2px;"></div>
                    </div>
                </div>
                <div class="player-card">
                    <div style="position: absolute; top: -4px; left: -4px; background: #ff6b35; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 10px; font-weight: bold;">4</div>
                    <div style="font-size: 20px; margin-bottom: 4px;">🛡️</div>
                    <div style="font-size: 11px; font-weight: bold; text-align: center; margin: 4px 0;">PALADIN</div>
                    <div style="display: flex; justify-content: space-between; width: 100%; font-size: 10px; font-weight: bold;">
                        <span style="color: #ff1744;">⚔️75</span>
                        <span style="color: #ff6b35;">⚡12</span>
                    </div>
                    <div style="width: 100%; height: 4px; background: rgba(0,0,0,0.3); border-radius: 2px; margin-top: 4px;">
                        <div style="width: 85%; height: 100%; background: linear-gradient(90deg, #004e89, #ff6b35); border-radius: 2px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="battle-controls">
            <button class="battle-btn">📊 STATS</button>
            <button class="battle-btn">⏩ 2X</button>
            <button class="battle-btn">⏸️ PAUSE</button>
            <button class="battle-btn">📷 CAP</button>
        </div>
    </div>

    <!-- 動漫卡通風格 -->
    <div class="design-container anime">
        <div class="design-title">🌈 動漫卡通風格</div>
        
        <div class="battle-header">
            <div class="stage-info">✨ 第1-5關 ✨</div>
            <div class="header-controls">
                <button title="暫停">⏸️</button>
                <button title="音效">🔊</button>
                <button title="設定">⚙️</button>
            </div>
        </div>

        <div class="enemy-section">
            <div class="section-header">
                <span>🔴 壞蛋隊伍 (3/3)</span>
                <span>💖 450</span>
            </div>
            <div style="display: flex; justify-content: center; gap: 15px; margin-top: 30px;">
                <div class="battle-card">
                    <div style="font-size: 24px; margin-bottom: 6px;">🐺</div>
                    <div style="display: flex; gap: 6px; font-size: 11px; font-weight: bold;">
                        <span style="color: #ff1493;">⚔️65</span>
                        <span style="color: #ffd700;">⚡12</span>
                    </div>
                    <div style="width: 100%; height: 6px; background: rgba(255, 20, 147, 0.2); border-radius: 10px; margin-top: 6px; border: 1px solid #ff1493;">
                        <div style="width: 80%; height: 100%; background: linear-gradient(90deg, #ff1493, #ffd700); border-radius: 10px;"></div>
                    </div>
                </div>
                <div class="battle-card">
                    <div style="font-size: 24px; margin-bottom: 6px;">🧌</div>
                    <div style="display: flex; gap: 6px; font-size: 11px; font-weight: bold;">
                        <span style="color: #ff1493;">⚔️85</span>
                        <span style="color: #ffd700;">⚡8</span>
                    </div>
                    <div style="width: 100%; height: 6px; background: rgba(255, 20, 147, 0.2); border-radius: 10px; margin-top: 6px; border: 1px solid #ff1493;">
                        <div style="width: 60%; height: 100%; background: linear-gradient(90deg, #ff1493, #ffd700); border-radius: 10px;"></div>
                    </div>
                </div>
                <div class="battle-card">
                    <div style="font-size: 24px; margin-bottom: 6px;">🕷️</div>
                    <div style="display: flex; gap: 6px; font-size: 11px; font-weight: bold;">
                        <span style="color: #ff1493;">⚔️45</span>
                        <span style="color: #ffd700;">⚡18</span>
                    </div>
                    <div style="width: 100%; height: 6px; background: rgba(255, 20, 147, 0.2); border-radius: 10px; margin-top: 6px; border: 1px solid #ff1493;">
                        <div style="width: 90%; height: 100%; background: linear-gradient(90deg, #ff1493, #ffd700); border-radius: 10px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="action-section">
            <div class="next-action">🌟 下一個行動: 火焰戰士 (2秒) 🌟</div>
            <div style="display: flex; justify-content: center; gap: 12px; align-items: center;">
                <div class="action-indicator player">你</div>
                <div class="action-indicator enemy">敵</div>
                <div class="action-indicator player">你</div>
                <div class="action-indicator enemy">敵</div>
                <div class="action-indicator player">你</div>
            </div>
        </div>

        <div class="player-section">
            <div class="section-header player-header" style="color: #87ceeb;">
                <span>🔵 我的隊伍 (3/3)</span>
                <span>💖 850</span>
            </div>
            <div style="display: flex; justify-content: center; gap: 15px; margin-top: 30px;">
                <div class="player-card ready">
                    <div style="position: absolute; top: -8px; left: -8px; background: #ff1493; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; border: 2px solid #ffd700;">5</div>
                    <div style="font-size: 24px; margin-bottom: 6px;">🔥</div>
                    <div style="font-size: 12px; font-weight: bold; text-align: center; margin: 6px 0; color: #ff1493; font-family: 'Comic Sans MS', cursive;">火龍戰士</div>
                    <div style="display: flex; justify-content: space-between; width: 100%; font-size: 11px; font-weight: bold;">
                        <span style="color: #ff1493;">⚔️120</span>
                        <span style="color: #ffd700;">⚡15</span>
                    </div>
                    <div style="width: 100%; height: 6px; background: rgba(135, 206, 235, 0.2); border-radius: 10px; margin-top: 6px; border: 1px solid #87ceeb;">
                        <div style="width: 100%; height: 100%; background: linear-gradient(90deg, #87ceeb, #ffd700); border-radius: 10px;"></div>
                    </div>
                </div>
                <div class="player-card">
                    <div style="position: absolute; top: -8px; left: -8px; background: #ff1493; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; border: 2px solid #ffd700;">3</div>
                    <div style="font-size: 24px; margin-bottom: 6px;">🏹</div>
                    <div style="font-size: 12px; font-weight: bold; text-align: center; margin: 6px 0; color: #87ceeb; font-family: 'Comic Sans MS', cursive;">月光射手</div>
                    <div style="display: flex; justify-content: space-between; width: 100%; font-size: 11px; font-weight: bold;">
                        <span style="color: #ff1493;">⚔️85</span>
                        <span style="color: #ffd700;">⚡18</span>
                    </div>
                    <div style="width: 100%; height: 6px; background: rgba(135, 206, 235, 0.2); border-radius: 10px; margin-top: 6px; border: 1px solid #87ceeb;">
                        <div style="width: 75%; height: 100%; background: linear-gradient(90deg, #87ceeb, #ffd700); border-radius: 10px;"></div>
                    </div>
                </div>
                <div class="player-card">
                    <div style="position: absolute; top: -8px; left: -8px; background: #ff1493; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; border: 2px solid #ffd700;">4</div>
                    <div style="font-size: 24px; margin-bottom: 6px;">🛡️</div>
                    <div style="font-size: 12px; font-weight: bold; text-align: center; margin: 6px 0; color: #87ceeb; font-family: 'Comic Sans MS', cursive;">聖騎士</div>
                    <div style="display: flex; justify-content: space-between; width: 100%; font-size: 11px; font-weight: bold;">
                        <span style="color: #ff1493;">⚔️75</span>
                        <span style="color: #ffd700;">⚡12</span>
                    </div>
                    <div style="width: 100%; height: 6px; background: rgba(135, 206, 235, 0.2); border-radius: 10px; margin-top: 6px; border: 1px solid #87ceeb;">
                        <div style="width: 85%; height: 100%; background: linear-gradient(90deg, #87ceeb, #ffd700); border-radius: 10px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="battle-controls">
            <button class="battle-btn">📊 數據</button>
            <button class="battle-btn">⏩ 快轉</button>
            <button class="battle-btn">⏸️ 暫停</button>
            <button class="battle-btn">📷 拍照</button>
        </div>
    </div>

    <!-- 暗黑哥特風格 -->
    <div class="design-container gothic">
        <div class="design-title">🖤 暗黑哥特風格</div>
        
        <div class="battle-header">
            <div class="stage-info">⚔️ Chamber 1-5</div>
            <div class="header-controls">
                <button title="暫停">⏸️</button>
                <button title="音效">🔊</button>
                <button title="設定">⚙️</button>
            </div>
        </div>

        <div class="enemy-section">
            <div class="section-header">
                <span>🔴 DARK LEGION (3/3)</span>
                <span>💀 450</span>
            </div>
            <div style="display: flex; justify-content: center; gap: 12px; margin-top: 30px;">
                <div class="battle-card">
                    <div style="font-size: 20px; margin-bottom: 4px; filter: drop-shadow(0 0 5px #8b0000);">🐺</div>
                    <div style="display: flex; gap: 4px; font-size: 10px; font-weight: bold;">
                        <span style="color: #dc143c;">⚔️65</span>
                        <span style="color: #8b0000;">⚡12</span>
                    </div>
                    <div style="width: 100%; height: 4px; background: rgba(0,0,0,0.5); border-radius: 0; margin-top: 4px; border: 1px solid #8b0000;">
                        <div style="width: 80%; height: 100%; background: linear-gradient(90deg, #8b0000, #dc143c); border-radius: 0;"></div>
                    </div>
                </div>
                <div class="battle-card">
                    <div style="font-size: 20px; margin-bottom: 4px; filter: drop-shadow(0 0 5px #8b0000);">🧌</div>
                    <div style="display: flex; gap: 4px; font-size: 10px; font-weight: bold;">
                        <span style="color: #dc143c;">⚔️85</span>
                        <span style="color: #8b0000;">⚡8</span>
                    </div>
                    <div style="width: 100%; height: 4px; background: rgba(0,0,0,0.5); border-radius: 0; margin-top: 4px; border: 1px solid #8b0000;">
                        <div style="width: 60%; height: 100%; background: linear-gradient(90deg, #8b0000, #dc143c); border-radius: 0;"></div>
                    </div>
                </div>
                <div class="battle-card">
                    <div style="font-size: 20px; margin-bottom: 4px; filter: drop-shadow(0 0 5px #8b0000);">🕷️</div>
                    <div style="display: flex; gap: 4px; font-size: 10px; font-weight: bold;">
                        <span style="color: #dc143c;">⚔️45</span>
                        <span style="color: #8b0000;">⚡18</span>
                    </div>
                    <div style="width: 100%; height: 4px; background: rgba(0,0,0,0.5); border-radius: 0; margin-top: 4px; border: 1px solid #8b0000;">
                        <div style="width: 90%; height: 100%; background: linear-gradient(90deg, #8b0000, #dc143c); border-radius: 0;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="action-section">
            <div class="next-action">⚡ NEXT SOUL: Crimson Warrior (2s)</div>
            <div style="display: flex; justify-content: center; gap: 8px; align-items: center;">
                <div class="action-indicator player">YOU</div>
                <div class="action-indicator enemy">FOE</div>
                <div class="action-indicator player">YOU</div>
                <div class="action-indicator enemy">FOE</div>
                <div class="action-indicator player">YOU</div>
            </div>
        </div>

        <div class="player-section">
            <div class="section-header player-header" style="color: #2d1b69;">
                <span>🔵 YOUR LEGION (3/3)</span>
                <span>💀 850</span>
            </div>
            <div style="display: flex; justify-content: center; gap: 12px; margin-top: 30px;">
                <div class="player-card ready">
                    <div style="position: absolute; top: -4px; left: -4px; background: #8b0000; color: #f8f8ff; border-radius: 0; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 10px; font-weight: bold; border: 1px solid #dc143c;">5</div>
                    <div style="font-size: 20px; margin-bottom: 4px; filter: drop-shadow(0 0 5px #2d1b69);">🔥</div>
                    <div style="font-size: 11px; font-weight: bold; text-align: center; margin: 4px 0; color: #f8f8ff; font-family: serif;">Crimson Warrior</div>
                    <div style="display: flex; justify-content: space-between; width: 100%; font-size: 10px; font-weight: bold;">
                        <span style="color: #dc143c;">⚔️120</span>
                        <span style="color: #8b0000;">⚡15</span>
                    </div>
                    <div style="width: 100%; height: 4px; background: rgba(0,0,0,0.5); border-radius: 0; margin-top: 4px; border: 1px solid #2d1b69;">
                        <div style="width: 100%; height: 100%; background: linear-gradient(90deg, #2d1b69, #8b0000); border-radius: 0;"></div>
                    </div>
                </div>
                <div class="player-card">
                    <div style="position: absolute; top: -4px; left: -4px; background: #8b0000; color: #f8f8ff; border-radius: 0; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 10px; font-weight: bold; border: 1px solid #dc143c;">3</div>
                    <div style="font-size: 20px; margin-bottom: 4px; filter: drop-shadow(0 0 5px #2d1b69);">🏹</div>
                    <div style="font-size: 11px; font-weight: bold; text-align: center; margin: 4px 0; color: #f8f8ff; font-family: serif;">Shadow Archer</div>
                    <div style="display: flex; justify-content: space-between; width: 100%; font-size: 10px; font-weight: bold;">
                        <span style="color: #dc143c;">⚔️85</span>
                        <span style="color: #8b0000;">⚡18</span>
                    </div>
                    <div style="width: 100%; height: 4px; background: rgba(0,0,0,0.5); border-radius: 0; margin-top: 4px; border: 1px solid #2d1b69;">
                        <div style="width: 75%; height: 100%; background: linear-gradient(90deg, #2d1b69, #8b0000); border-radius: 0;"></div>
                    </div>
                </div>
                <div class="player-card">
                    <div style="position: absolute; top: -4px; left: -4px; background: #8b0000; color: #f8f8ff; border-radius: 0; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 10px; font-weight: bold; border: 1px solid #dc143c;">4</div>
                    <div style="font-size: 20px; margin-bottom: 4px; filter: drop-shadow(0 0 5px #2d1b69);">🛡️</div>
                    <div style="font-size: 11px; font-weight: bold; text-align: center; margin: 4px 0; color: #f8f8ff; font-family: serif;">Death Knight</div>
                    <div style="display: flex; justify-content: space-between; width: 100%; font-size: 10px; font-weight: bold;">
                        <span style="color: #dc143c;">⚔️75</span>
                        <span style="color: #8b0000;">⚡12</span>
                    </div>
                    <div style="width: 100%; height: 4px; background: rgba(0,0,0,0.5); border-radius: 0; margin-top: 4px; border: 1px solid #2d1b69;">
                        <div style="width: 85%; height: 100%; background: linear-gradient(90deg, #2d1b69, #8b0000); border-radius: 0;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="battle-controls">
            <button class="battle-btn">📊 STATS</button>
            <button class="battle-btn">⏩ HASTE</button>
            <button class="battle-btn">⏸️ PAUSE</button>
            <button class="battle-btn">📷 CAPTURE</button>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.querySelectorAll('.battle-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const originalText = this.textContent;
                
                if (this.closest('.esports')) {
                    this.textContent = this.textContent.includes('PAUSE') ? '▶️ PLAY' : 
                                     this.textContent.includes('2X') ? '⏩ 4X' : 
                                     originalText;
                } else if (this.closest('.anime')) {
                    this.style.transform = 'scale(0.9) rotate(-5deg)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                } else if (this.closest('.gothic')) {
                    this.style.textShadow = '0 0 15px #dc143c';
                    setTimeout(() => {
                        this.style.textShadow = '0 0 5px #8b0000';
                    }, 300);
                }
            });
        });

        // 模擬戰鬥動畫
        function simulateBattle() {
            document.querySelectorAll('.battle-card, .player-card').forEach(card => {
                if (Math.random() < 0.3) {
                    if (card.closest('.esports')) {
                        card.style.animation = 'esportsPulse 0.5s ease';
                    } else if (card.closest('.anime')) {
                        card.style.animation = 'rainbowPulse 0.5s ease';
                    } else if (card.closest('.gothic')) {
                        card.style.animation = 'bloodPulse 0.5s ease';
                    }
                    
                    setTimeout(() => {
                        card.style.animation = '';
                    }, 500);
                }
            });
        }

        // 每4秒模擬一次戰鬥動作
        setInterval(simulateBattle, 4000);
    </script>
</body>
</html>