<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Battle UI Layout - PetingGame</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Times New Roman', serif;
            background: linear-gradient(135deg, #f7f3e9 0%, #e8dcc0 50%, #d4c5a0 100%);
            min-height: 100vh;
            color: #3c2415;
            overflow-x: hidden;
        }

        /* 手機直向戰鬥佈局 */
        .battle-container {
            max-width: 375px;
            margin: 0 auto;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 頂部控制欄 */
        .battle-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            background: rgba(139, 69, 19, 0.2);
            backdrop-filter: blur(10px);
            border-bottom: 2px dashed rgba(139, 69, 19, 0.3);
        }

        .stage-info {
            font-size: 16px;
            font-weight: bold;
            font-family: 'Times New Roman', serif;
            color: #8b4513;
            transform: rotate(-2deg);
        }

        .header-controls {
            display: flex;
            gap: 12px;
        }

        .control-btn {
            width: 36px;
            height: 36px;
            background: #f4a261;
            border: 2px solid #8b4513;
            border-radius: 8px;
            color: #8b4513;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 2px 2px 0px rgba(139, 69, 19, 0.3);
            transform: rotate(-2deg);
        }

        .control-btn:hover {
            background: #f59e0b;
            transform: rotate(0deg) scale(1.05);
            box-shadow: 3px 3px 0px rgba(139, 69, 19, 0.4);
        }

        /* 敵方區域 (30%) */
        .enemy-section {
            background: rgba(220, 38, 38, 0.1);
            padding: 15px;
            border-bottom: 3px dashed rgba(139, 69, 19, 0.3);
            min-height: 30vh;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Times New Roman', serif;
        }

        .enemy-header {
            color: #dc2626;
            font-family: 'Times New Roman', serif;
            transform: rotate(-1deg);
        }

        .cards-grid {
            display: flex;
            justify-content: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .battle-card {
            width: 80px;
            height: 100px;
            background: linear-gradient(145deg, #f9f7f4, #f0ede6);
            border: 3px solid #8b4513;
            border-radius: 8px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            padding: 8px 4px;
            font-size: 12px;
            font-family: 'Times New Roman', serif;
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.3);
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
        }

        .battle-card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 30%, rgba(139, 69, 19, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 70% 60%, rgba(139, 69, 19, 0.05) 1px, transparent 1px),
                radial-gradient(circle at 40% 80%, rgba(139, 69, 19, 0.08) 1px, transparent 1px);
            background-size: 20px 20px, 15px 15px, 25px 25px;
            z-index: 1;
        }

        .battle-card:after {
            content: '';
            position: absolute;
            top: 6px;
            left: 6px;
            right: 6px;
            bottom: 6px;
            border: 2px dashed #8b4513;
            border-radius: 4px;
            opacity: 0.3;
            z-index: 1;
        }

        .battle-card.enemy {
            border-color: rgba(220, 38, 38, 0.8);
            background: linear-gradient(145deg, #f9f7f4, #f0ede6);
        }

        .battle-card.player {
            border-color: rgba(34, 139, 34, 0.8);
            background: linear-gradient(145deg, #f9f7f4, #f0ede6);
        }

        .battle-card:hover {
            transform: translateY(-4px) rotate(2deg);
            box-shadow: 0 12px 30px rgba(139, 69, 19, 0.4);
            filter: drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.2));
        }

        .card-icon {
            font-size: 24px;
            margin-bottom: 4px;
            z-index: 2;
            position: relative;
            filter: sepia(0.3) contrast(1.2);
            animation: handDrawnBob 4s infinite ease-in-out;
            transform: rotate(-2deg);
        }

        .card-stats {
            display: flex;
            gap: 4px;
            font-size: 10px;
            font-weight: bold;
            z-index: 2;
            position: relative;
            color: #8b4513;
            font-family: 'Times New Roman', serif;
        }

        .hp-bar {
            width: 100%;
            height: 4px;
            background: rgba(139, 69, 19, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 4px;
            border: 1px solid #8b4513;
            z-index: 2;
            position: relative;
        }

        .hp-fill {
            height: 100%;
            background: linear-gradient(90deg, #dc2626, #b91c1c);
            transition: width 0.3s ease;
            position: relative;
        }

        .hp-fill:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(255, 255, 255, 0.3) 2px,
                rgba(255, 255, 255, 0.3) 4px
            );
        }

        .hp-fill.player {
            background: linear-gradient(90deg, #228b22, #006400);
        }

        /* 中間行動區域 */
        .action-section {
            background: rgba(139, 69, 19, 0.15);
            padding: 10px 20px;
            backdrop-filter: blur(5px);
            border-top: 2px dashed rgba(139, 69, 19, 0.3);
            border-bottom: 2px dashed rgba(139, 69, 19, 0.3);
        }

        .next-action {
            font-size: 14px;
            margin-bottom: 8px;
            text-align: center;
            font-family: 'Times New Roman', serif;
            font-weight: bold;
            color: #8b4513;
            transform: rotate(-1deg);
        }

        .action-queue {
            display: flex;
            justify-content: center;
            gap: 8px;
            align-items: center;
        }

        .action-indicator {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            border: 2px solid #8b4513;
            font-family: 'Times New Roman', serif;
        }

        .action-indicator.player {
            background: #228b22;
            color: white;
            box-shadow: 2px 2px 0px rgba(139, 69, 19, 0.3);
            transform: rotate(-3deg);
        }

        .action-indicator.enemy {
            background: #dc2626;
            color: white;
            box-shadow: 2px 2px 0px rgba(139, 69, 19, 0.3);
            transform: rotate(2deg);
        }

        /* 玩家區域 (40%) */
        .player-section {
            background: rgba(34, 139, 34, 0.1);
            padding: 15px;
            border-bottom: 3px dashed rgba(139, 69, 19, 0.3);
            min-height: 40vh;
        }

        .player-header {
            color: #228b22;
            font-family: 'Times New Roman', serif;
            transform: rotate(1deg);
        }

        .player-cards {
            display: flex;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .player-card {
            width: 90px;
            height: 120px;
            background: linear-gradient(145deg, #f9f7f4, #f0ede6);
            border: 3px solid #8b4513;
            border-radius: 8px;
            padding: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            font-family: 'Times New Roman', serif;
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.3);
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
        }

        .player-card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 30%, rgba(139, 69, 19, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 70% 60%, rgba(139, 69, 19, 0.05) 1px, transparent 1px),
                radial-gradient(circle at 40% 80%, rgba(139, 69, 19, 0.08) 1px, transparent 1px);
            background-size: 20px 20px, 15px 15px, 25px 25px;
        }

        .player-card:after {
            content: '';
            position: absolute;
            top: 6px;
            left: 6px;
            right: 6px;
            bottom: 6px;
            border: 2px dashed #8b4513;
            border-radius: 4px;
            opacity: 0.3;
        }

        .player-card:hover {
            transform: translateY(-4px) rotate(2deg);
            box-shadow: 0 12px 30px rgba(139, 69, 19, 0.4);
            filter: drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.2));
        }

        .player-card.ready {
            border-color: #228b22;
            box-shadow: 0 0 15px rgba(34, 139, 34, 0.4);
            animation: pulse 1.5s infinite;
        }

        .card-level {
            position: absolute;
            top: -4px;
            left: -4px;
            background: #f4a261;
            color: white;
            border: 2px solid #8b4513;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            font-family: 'Times New Roman', serif;
            box-shadow: 2px 2px 0px rgba(139, 69, 19, 0.3);
            transform: rotate(-5deg);
            z-index: 3;
        }

        .card-name {
            font-size: 11px;
            font-weight: bold;
            text-align: center;
            margin: 4px 0;
            font-family: 'Times New Roman', serif;
            color: #8b4513;
            z-index: 2;
            position: relative;
            transform: rotate(1deg);
            text-decoration: underline;
            text-decoration-color: rgba(139, 69, 19, 0.3);
        }

        .card-bottom {
            display: flex;
            justify-content: space-between;
            width: 100%;
            font-size: 11px;
            font-weight: bold;
            font-family: 'Times New Roman', serif;
            color: #8b4513;
            z-index: 2;
            position: relative;
        }

        .card-bottom span:first-child {
            color: #d2691e;
            transform: rotate(-1deg);
        }

        .card-bottom span:last-child {
            color: #228b22;
            transform: rotate(1deg);
        }

        /* 底部控制欄 */
        .battle-controls {
            background: rgba(139, 69, 19, 0.3);
            backdrop-filter: blur(20px);
            padding: 15px 20px;
            display: flex;
            justify-content: space-around;
            gap: 10px;
            border-top: 3px dashed rgba(139, 69, 19, 0.5);
        }

        .battle-btn {
            flex: 1;
            padding: 12px 8px;
            background: #f4a261;
            border: 2px solid #8b4513;
            border-radius: 8px;
            color: #8b4513;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            font-family: 'Times New Roman', serif;
            box-shadow: 2px 2px 0px rgba(139, 69, 19, 0.3);
            transform: rotate(-1deg);
        }

        .battle-btn:hover {
            background: #f59e0b;
            transform: rotate(0deg) translateY(-1px);
            box-shadow: 3px 3px 0px rgba(139, 69, 19, 0.4);
        }

        .battle-btn.speed {
            background: rgba(34, 139, 34, 0.8);
            border-color: #8b4513;
            color: white;
            transform: rotate(1deg);
        }

        .battle-btn.pause {
            background: rgba(220, 38, 38, 0.8);
            border-color: #8b4513;
            color: white;
            transform: rotate(-2deg);
        }

        /* 動畫效果 */
        @keyframes pulse {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.05) rotate(1deg); }
        }

        @keyframes damage {
            0% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(0.95) rotate(-3deg); background: rgba(220, 38, 38, 0.5); }
            100% { transform: scale(1) rotate(0deg); }
        }

        @keyframes handDrawnBob {
            0%, 100% { transform: translateY(0) rotate(-2deg); }
            50% { transform: translateY(-2px) rotate(0deg); }
        }

        .damage-animation {
            animation: damage 0.5s ease;
        }

        /* 響應式調整 */
        @media (max-width: 360px) {
            .battle-container {
                max-width: 100%;
            }
            
            .battle-card, .player-card {
                width: 70px;
                height: 90px;
            }
            
            .card-icon {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="battle-container">
        <!-- 頂部控制欄 -->
        <div class="battle-header">
            <div class="stage-info">⚔️ Stage 1-5</div>
            <div class="header-controls">
                <button class="control-btn" title="暫停">⏸️</button>
                <button class="control-btn" title="音效">🔊</button>
                <button class="control-btn" title="設定">⚙️</button>
            </div>
        </div>

        <!-- 敵方區域 -->
        <div class="enemy-section">
            <div class="section-header enemy-header">
                <span>🔴 ENEMY (3/3)</span>
                <span>❤️ 450</span>
            </div>
            <div class="cards-grid">
                <div class="battle-card enemy">
                    <div class="card-icon">🐺</div>
                    <div class="card-stats">
                        <span>⚔️65</span>
                        <span>⚡12</span>
                    </div>
                    <div class="hp-bar">
                        <div class="hp-fill" style="width: 80%;"></div>
                    </div>
                </div>
                <div class="battle-card enemy">
                    <div class="card-icon">🧌</div>
                    <div class="card-stats">
                        <span>⚔️85</span>
                        <span>⚡8</span>
                    </div>
                    <div class="hp-bar">
                        <div class="hp-fill" style="width: 60%;"></div>
                    </div>
                </div>
                <div class="battle-card enemy">
                    <div class="card-icon">🕷️</div>
                    <div class="card-stats">
                        <span>⚔️45</span>
                        <span>⚡18</span>
                    </div>
                    <div class="hp-bar">
                        <div class="hp-fill" style="width: 90%;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中間行動區域 -->
        <div class="action-section">
            <div class="next-action">⚡ 下一個: 火龍戰士 (2秒)</div>
            <div class="action-queue">
                <div class="action-indicator player">你</div>
                <div class="action-indicator enemy">敵</div>
                <div class="action-indicator player">你</div>
                <div class="action-indicator enemy">敵</div>
                <div class="action-indicator player">你</div>
            </div>
        </div>

        <!-- 玩家區域 -->
        <div class="player-section">
            <div class="section-header player-header">
                <span>🔵 YOUR TEAM (3/3)</span>
                <span>❤️ 850</span>
            </div>
            <div class="player-cards">
                <div class="player-card ready">
                    <div class="card-level">5</div>
                    <div class="card-icon">🔥</div>
                    <div class="card-name">火龍戰士</div>
                    <div class="card-bottom">
                        <span>⚔️120</span>
                        <span>⚡15</span>
                    </div>
                    <div class="hp-bar">
                        <div class="hp-fill player" style="width: 100%;"></div>
                    </div>
                </div>
                <div class="player-card">
                    <div class="card-level">3</div>
                    <div class="card-icon">🏹</div>
                    <div class="card-name">月光射手</div>
                    <div class="card-bottom">
                        <span>⚔️85</span>
                        <span>⚡18</span>
                    </div>
                    <div class="hp-bar">
                        <div class="hp-fill player" style="width: 75%;"></div>
                    </div>
                </div>
                <div class="player-card">
                    <div class="card-level">4</div>
                    <div class="card-icon">🛡️</div>
                    <div class="card-name">聖騎士</div>
                    <div class="card-bottom">
                        <span>⚔️75</span>
                        <span>⚡12</span>
                    </div>
                    <div class="hp-bar">
                        <div class="hp-fill player" style="width: 85%;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部控制欄 -->
        <div class="battle-controls">
            <button class="battle-btn">📊 詳情</button>
            <button class="battle-btn speed">⏩ 2x</button>
            <button class="battle-btn pause">⏸️ 暫停</button>
            <button class="battle-btn">📷 截圖</button>
        </div>
    </div>

    <script>
        // 戰鬥控制交互
        let battleSpeed = 1;
        let isPaused = false;

        // 速度控制
        document.querySelector('.battle-btn.speed').addEventListener('click', function() {
            battleSpeed = battleSpeed >= 4 ? 1 : battleSpeed * 2;
            this.textContent = `⏩ ${battleSpeed}x`;
        });

            // 暫停控制
        document.querySelector('.battle-btn.pause').addEventListener('click', function() {
            isPaused = !isPaused;
            this.textContent = isPaused ? '▶️ 繼續' : '⏸️ 暫停';
            this.style.background = isPaused ? 'rgba(34, 139, 34, 0.8)' : 'rgba(220, 38, 38, 0.8)';
            this.style.transform = isPaused ? 'rotate(2deg)' : 'rotate(-2deg)';
        });

        // 模擬戰鬥動畫
        function simulateBattle() {
            const cards = document.querySelectorAll('.battle-card, .player-card');
            const randomCard = cards[Math.floor(Math.random() * cards.length)];
            
            // 添加傷害動畫
            randomCard.classList.add('damage-animation');
            
            // 隨機減少血量
            const hpBar = randomCard.querySelector('.hp-fill');
            if (hpBar) {
                const currentWidth = parseInt(hpBar.style.width) || 100;
                const newWidth = Math.max(0, currentWidth - Math.random() * 20);
                hpBar.style.width = newWidth + '%';
            }
            
            setTimeout(() => {
                randomCard.classList.remove('damage-animation');
            }, 500);
        }

        // 每3秒模擬一次戰鬥動作
        setInterval(() => {
            if (!isPaused) {
                simulateBattle();
            }
        }, 3000);

        // 卡牌點擊效果
        document.querySelectorAll('.battle-card, .player-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(1.1) rotate(5deg)';
                this.style.filter = 'drop-shadow(6px 6px 12px rgba(0, 0, 0, 0.3))';
                setTimeout(() => {
                    this.style.transform = '';
                    this.style.filter = '';
                }, 300);
            });
        });
    </script>
</body>
</html>